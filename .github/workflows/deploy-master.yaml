name: Deploying to Production
on:
    push:
      branches:
        - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1
    
    - name: Login to SWR
      uses: huaweicloud/swr-login@v2.1.0
      with:
        access-key-id: ${{ secrets.ACCESS_KEY_ID }}
        access-key-secret: ${{ secrets.ACCESS_KEY_SECRET }}
        region: af-south-1

    - name: Build and push Docker image
      run: |
        docker build -t swr.af-south-1.myhuaweicloud.com/credpal-prod/card-service:prod .
        docker push swr.af-south-1.myhuaweicloud.com/credpal-prod/card-service:prod  

    - name: Deploy to Coolify
      env:
        COOLIFY_API_TOKEN: ${{ secrets.COOLIFY_TOKEN }}
        COOLIFY_URL: ${{ secrets.COOLIFY_URL }}
        COOLIFY_APP_ID: card-service:prod
      run: |
        curl $COOLIFY_URL/api/v1/deploy?tag=$COOLIFY_APP_ID \
          -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
          -H "Content-Type: application/json" 
            
      if: ${{ always() }} # Use always to ensure that the notification is also send on failure of former steps
    - name: Notify Google space 
      uses: SimonScholz/google-chat-action@main
      with:
        webhookUrl: '${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}'
        title: Deployment
        jobStatus: '${{ job.status }}'
