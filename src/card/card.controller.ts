import {
  Controller,
  Post,
  Get,
  Patch,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CardService } from './card.service';
import {
  ActivatePhysicalCardDto,
  DeleteCardDto,
  FreezeCardDto,
  GetCardOTP,
  GetCardTransactionByIdDto,
  GetCardTransactionsDto,
  InitiateCreateCardDto,
  InitiateFundCardDto,
  RegisterUser,
  UnfreezeCardDto,
  UnloadCardDto,
  UpdateCardPinDto,
  UpdateTransactionByIdDto,
} from './dto/card.dto';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { VersionBuildGuard } from '@crednet/utils';

@Controller('cards')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('card service')
export class CardController {
  constructor(private readonly cardService: CardService) {}

  @Post('register-user')
  async registerUser(
    @GetAuthData() userData: AuthData,
    @Body() dto: RegisterUser,
  ) {
    return this.cardService.register(userData, dto);
  }

  @Post('initialize/create-card')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async InitializeCreateCard(
    @GetAuthData() userData: AuthData,
    @Body() dto: InitiateCreateCardDto,
  ) {
    return this.cardService.initiateCreate(userData, dto);
  }

  // @Post('create-card')
  // async createCard(@Body() dto: CreateCardDto) {
  //   return this.cardService.createCard(dto);
  // }

  @Post('activate')
  @UseGuards(PinGuard)
  @PinRequirement('pin')
  async activatePhysicalCard(
    @GetAuthData() userData: AuthData,
    @Body() dto: ActivatePhysicalCardDto,
  ) {
    return this.cardService.activatePhysicalCard(userData, dto);
  }

  @Get()
  async getUserCards(@GetAuthData() userData: AuthData) {
    return this.cardService.getUserCards(userData.id + '');
  }

  @Get('otp')
  async getCardOTP(@Body() dto: GetCardOTP) {
    return this.cardService.getSpendOtp(dto.cardId, dto.amount);
  }

  @Get('registered')
  async checkUserEligible(@GetAuthData() userData: AuthData) {
    return this.cardService.isUserRegistered(userData.id + '');
  }

  @Get('update-details/:cardId')
  async getCardDetails(@Param('cardId') cardId: string) {
    return this.cardService.getCardDetails(cardId);
  }

  @Get('balance/:cardId')
  async getCardBalance(@Param('cardId') cardId: string) {
    return this.cardService.getCardBalance(cardId);
  }

  @Post('initialize/fund')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async initializeFundCard(
    @GetAuthData() userData: AuthData,
    @Body() dto: InitiateFundCardDto,
  ) {
    return this.cardService.initiateFunding(userData, dto);
  }

  // @Post('fund')
  // async fundCard(@Body() dto: FundCardDto) {
  //   return this.cardService.fundCard(dto);
  // }

  // @Post('initialize/withdraw')
  // async initializeWithdrawCard(@Body() dto: InitiateFundCardDto) {
  //   return this.cardService.initiateWithdrawal(dto);
  // }

  @Post('withdraw')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async unloadCard(
    @GetAuthData() userData: AuthData,
    @Body() dto: UnloadCardDto,
  ) {
    
    return this.cardService.unloadCard(userData, dto);
  }

  @Get('transactions')
  async getMainCardTransactions(
    @GetAuthData() userData: AuthData,
    @Query() dto: GetCardTransactionsDto,
  ) {
    return this.cardService.getCardTransactions(userData, dto);
  }

  @Get('transaction/:transactionId')
  async getMainCardTransactionById(@Param() dto: GetCardTransactionByIdDto) {
    return this.cardService.getCardTransactionById(dto);
  }

  @Get('bridge-transactions')
  async getCardTransactions(@Query() dto: GetCardTransactionsDto) {
    return this.cardService.getBridgeCardTransactions(dto);
  }

  @Get('bridge-transaction/:cardId/:transactionId')
  async getCardTransactionById(@Param() dto: GetCardTransactionByIdDto) {
    return this.cardService.getBridgeCardTransactionById(dto);
  }

  // @Get('bridge-transaction/status/:cardId/:transactionId')
  // async getCardTransactionStatus(@Param() dto: GetCardTransactionByIdDto) {
  //   return this.cardService.getCardTransactionStatus(dto);
  // }

  @Get('all')
  async getAllCardholderCards(@GetAuthData() userData: AuthData) {
    return this.cardService.getAllCardholderCards(userData.id + '');
  }

  @Post('sync')
  async syncAllCardholderCards(@GetAuthData() userData: AuthData) {
    return this.cardService.syncAllCardholderCards(userData.id + '');
  }

  @Patch('transaction')
  async updateTransaction(@Body() dto: UpdateTransactionByIdDto) {
    return this.cardService.updateTransactionById(dto);
  }

  @Patch('freeze')
  async freezeCard(@Body() dto: FreezeCardDto) {
    return this.cardService.freezeCard(dto);
  }

  @Patch('unfreeze')
  async unfreezeCard(@Body() dto: UnfreezeCardDto) {
    return this.cardService.unfreezeCard(dto);
  }

  @Patch('delete')
  async deleteCard(@Body() dto: DeleteCardDto) {
    return this.cardService.deleteCard(dto);
  }

  @Patch('update-pin')
  async updateCardPin(@Body() dto: UpdateCardPinDto) {
    return this.cardService.updateCardPin(dto);
  }
}
