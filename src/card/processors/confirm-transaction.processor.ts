import { Processor, WorkerHost } from '@nestjs/bullmq';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Job } from 'bullmq';
import { Events, LocalQueues } from 'src/utils/enums';

@Processor(LocalQueues.TRANSACTION)
export class ConfirmTransactionProcessor extends WorkerHost {
  constructor(private readonly eventEmitter: EventEmitter2) {
    super();
  }

  async process(job: Job<any, any, string>): Promise<any> {
    try {
      switch (job.name) {
        case 'funding': {
          this.eventEmitter.emit(Events.VALIDATE_FUNDING_TRANSACTION, {
            id: job.data.id,
          });
          console.log(
            `Emitted: ${Events.VALIDATE_FUNDING_TRANSACTION} for ID: ${job.data.id}`,
          );
          break;
        }
        case 'withdrawal': {
          this.eventEmitter.emit(Events.VALIDATE_WITHDRAWAL_TRANSACTION, {
            id: job.data.id,
          });
          console.log(
            `Emitted: ${Events.VALIDATE_WITHDRAWAL_TRANSACTION} for ID: ${job.data.id}`,
          );
          break;
        }
        default:
          console.warn(`Unknown job type: ${job.name}`);
      }

      return { status: 'success', id: job.data.id };
    } catch (error) {
      console.error(`Error processing job ${job.id}:`, error);
      throw error; // Ensures BullMQ retries the job if applicable
    }
  }
}
