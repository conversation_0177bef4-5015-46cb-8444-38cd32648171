import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { Log } from '../../logs1/entities/log.entity';

@Injectable()
export class LogRepository extends TypeOrmRepository<Log> {
  constructor(private readonly dataSource: DataSource) {
    super(Log, dataSource.createEntityManager());
  }
}
