import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Transaction } from './transaction.entity';
import { Card } from './card.entity';
import { IdType } from 'src/utils/enums';

@Entity({
  name: 'user-data',
})
export class UserData extends BaseEntity {
  @Column()
  @Index()
  userId: string;

  @Column({ nullable: true })
  @Index()
  cardHolderId: string;

  @Column({ nullable: true })
  @Index()
  midenCardHolderId: string;

  @Column()
  bvn: string;

  // @Column({ nullable: true })
  // nin: string;

  @Column()
  idImage: string;

  @Column({ nullable: true })
  idNumber: string;

  @Column({ type: 'enum', enum: IdType })
  idType: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ nullable: true })
  dateOfBirth: string;

  @Column({ nullable: true, type: 'json' })
  address: any;

  @OneToMany(() => Transaction, (transaction) => transaction.user)
  transactions: Transaction[];

  @OneToMany(() => Card, (card) => card.user)
  cards: Card[];
}
