import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { Currency, PaymentTransactionWalletType } from '@crednet/utils';

import { UserData } from './user.entity';
import { BaseEntity } from 'src/config/repository/base-entity';
import {
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';
import { Card } from './card.entity';
import { CardProvider } from '../dto/card-provider';

@Entity({ name: 'transactions' })
@Index(['status'])
export class Transaction extends BaseEntity {
  @Index('userId')
  @Column({
    nullable: false,
  })
  userId: string;

  @Index('cardId')
  @Column({
    nullable: true,
  })
  cardId: string;

  @Column({ nullable: true })
  @Index('reference')
  reference: string;

  @Column('decimal', {
    nullable: false,
    precision: 10,  
    scale: 3,
  })
  amount: number;

  @Column('decimal', {
    nullable: false,
    precision: 10,  
    scale: 3,
  })
  fee: number;

  @Column({
    type: 'enum',
    enum: Currency,
  })
  @Index('currency')
  currency: Currency;

  @Column({
    nullable: false,
    type: 'enum',
    enum: TransactionType,
  })
  @Index('type')
  type: TransactionType;

  @Column({
    nullable: false,
    type: 'enum',
    enum: TransactionCategory,
  })
  @Index('category')
  category: TransactionCategory;

  @Column({
    nullable: true,
  })
  narration: string;

  @Column({
    nullable: false,
    type: 'enum',
    enum: TransactionStatus,
  })
  @Index('status')
  status: string;

  @Column({
    nullable: false,
    type: 'enum',
    enum: TransactionStatus,
  })
  @Index('payment-status')
  paymentStatus: TransactionStatus;

  @Column({
    type: 'enum',
    enum: [
      PaymentTransactionWalletType.CREDPAL_CASH,
      PaymentTransactionWalletType.CREDPAL_CREDIT,
    ],
    nullable: true,
  })
  walletType: PaymentTransactionWalletType;

  @Column({
    nullable: false,
    type: 'enum',
    enum: CardProvider,
    default: CardProvider.BRIDGECARD,
  })
  @Index('provider')
  provider: CardProvider;

  @Column({
    default: false,
  })
  isRefunded: boolean;

  @Column({ type: 'float', nullable: true })
  exchangeRate: number;

  @Column({ nullable: true, type: 'json' })
  metadata: any;

  @ManyToOne(() => Card, (card) => card.transactions)
  @JoinColumn({ name: 'cardId', referencedColumnName: 'id' })
  card: Card;

  @Column('datetime', { nullable: true })
  closingAt: Date;

  @ManyToOne(() => UserData, (user) => user)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: UserData;
}
