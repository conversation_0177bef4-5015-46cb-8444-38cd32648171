import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  Exchanges,
  MidenEventClass,
  MidenWebhookEvent,
  TransactionStatus,
} from 'src/utils/enums';
import { RabbitmqService } from '@crednet/utils';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MidenWebhookResponse } from '@app/miden/dto';
import { CardService } from './card.service';
import { NotificationService } from 'src/notification/notification.service';

@Injectable()
export class MidenWebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rabbitmqService: RabbitmqService,
    private readonly eventEmitter: EventEmitter2,
    private readonly cardService: CardService,
    private readonly notificationService: NotificationService
  ) {}

  onModuleInit() {
    for (const eventType of Object.values(MidenWebhookEvent)) {
      this.rabbitmqService.subscribe(
        `${Exchanges.WEBHOOK}.miden.${eventType}`,
        async ({ data: payload, message, ack }) => {
          console.log({  payload });
          try {
            await this.callFn(eventType, payload);
            ack();
          } catch (e) {
            console.log(e);
            throw e;
          }
        },
      );
    }
  }

  async callFn(event: string, data: MidenWebhookResponse) {
    switch (data.eventClass) {
      case MidenEventClass.PURCHASE:
      case MidenEventClass.SETTLEMENT:
      case MidenEventClass.CROSS_BORDER:
      case MidenEventClass.DECLINES:
        this.cardPurchased(event, data);
        // save transaction and send notification
        break;
      case MidenEventClass.CARD_TOPUP:
        this.cardService.verifyTransaction(data.data?.transactionReference);
        // finalize transaction
        break;
      case MidenEventClass.CARD_WITHDRAWAL:
        this.cardService.verifyTransaction(data.data?.transactionReference);
        // finalize transaction
        break;
      case MidenEventClass.CARD_BLOCK:
        this.cardBlocked(data);
        // if (data.eventType === MidenWebhookEvent.PURCHASE_CARD_AUTH_DECLINED_TERMINATE) {
        // finalize transaction
        break;
      case MidenEventClass.CARD_TERMINATION:
        this.cardTerminated(event, data.data);
        // finalize transaction
        break;

      case MidenEventClass.CARD_EXPIRATION:
        // finalize transaction
        break;
      // finalize transaction
      // break;
      // finalize transaction
      case MidenEventClass.REFUND_SETTLEMENT:
        await this.eventEmitter.emit(event, data);
        break;
    }
    // await this.eventEmitter.emit(event, data);
  }

  cardTerminated(
    event: string,
    data: {
      cardId: string;
      amount: number;
      reason: string;
      oldBalance: number;
      newBalance: number;
      transactionReference: string;
    },
  ) {
    // mark card as terminated, create a transaction to show the reason
  }

  cardBlocked(data: MidenWebhookResponse) {
    // finalize transaction
  }

  async cardPurchased(event: string, data: MidenWebhookResponse) {
    switch (data.eventType) {
      case MidenWebhookEvent.PURCHASE_CARD_ISSUED:
        // this.cardService.verifyTransaction(
        //   data.data?.incomingTransactionCode,
        //   'success',
        // );
        break;

      case MidenWebhookEvent.PURCHASE_CARD_AUTH_APPROVED:
        this.cardService.handleCharge(event, data, TransactionStatus.SUCCESS);
        // if(data.data.)
        // this.notificationService.cardlowBalance(transaction.user?.userId); 
        break;
      case MidenWebhookEvent.PURCHASE_CARD_AUTH_DECLINED:
      case MidenWebhookEvent.PURCHASE_CARD_DECLINE_CHARGE_SETTLED:
        this.cardService.handleCharge(event, data, TransactionStatus.FAILED);
        break;
      case MidenWebhookEvent.PURCHASE_CARD_AUTH_SETTLED:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.SUCCESS,
          'Authorization settled',
        );
        break;
      case MidenWebhookEvent.PURCHASE_CARD_AUTH_REVERSAL_SETTLED:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.SUCCESS,
          'Authorization refunded',
        );
        break;
      case MidenWebhookEvent.PURCHASE_CARD_RETURN_AUTH_APPROVED:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.SUCCESS,
          'Authorization returned',
        );
        break;

      case MidenWebhookEvent.PURCHASE_CARD_AUTH_REVERSAL_ISSUER_EXPIRATION:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.SUCCESS,
          'Authorization returned',
        );
        break;
      case MidenWebhookEvent.PURCHASE_CARD_CROSS_BORDER_PENDING:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.FAILED,
          'Cross border transaction pending',
        );
      case MidenWebhookEvent.PURCHASE_CARD_CROSS_BORDER_SETTLED:
        this.cardService.handleCharge(
          event,
          data,
          TransactionStatus.FAILED,
          'Transaction settled',
        );
        break;
    }
  }
}
