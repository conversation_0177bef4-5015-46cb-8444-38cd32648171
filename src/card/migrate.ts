import { BridgecardService } from '@app/bridgecard';
import { EncryptionService } from '@app/bridgecard/encryption.service';
import { GeolocationService } from '@app/geolocation';
import { MidenService } from '@app/miden';
import { VerificationService } from '@app/verification';
import {
  RabbitmqService,
  PaymentCacheService,
  Currency,
  RedisService,
  PaymentTransactionWalletType,
} from '@crednet/utils';
import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Queue } from 'bullmq';
import { ConfigurationsService } from 'src/configurations/configurations.service';
import {
  LocalQueues,
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';
import { CardProducer } from './card.producer';
import { CardRepository } from './repositories/card.repository';
import { TransactionRepository } from './repositories/transaction.repository';
import { UserDataRepository } from './repositories/user.repository';
import { Card } from './entities/card.entity';
import { CardProvider } from './dto/card-provider';
import { CardService } from './card.service';
import { randomUUID } from 'crypto';
import { In, IsNull, Not } from 'typeorm';
import { users } from './users';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly logger = new Logger(MigrationService.name);
  private readonly BATCH_SIZE = 10;
  private readonly REDIS_BALANCE_KEY_PREFIX = 'bridgecard_balance:';

  constructor(
    private readonly bridgecardService: BridgecardService,
    private readonly cardRepository: CardRepository,
    private readonly userRepository: UserDataRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly cardProducer: CardProducer,
    private readonly currencyConverter: ConfigurationsService,
    private readonly cardService: CardService,
    private readonly encryptionService: EncryptionService,
    private readonly verificationService: VerificationService,
    // @InjectQueue(LocalQueues.TRANSACTION)
    // private readonly transactionQueue: Queue,
    private readonly midenService: MidenService,
    private readonly rmqService: RabbitmqService,
    private readonly redisService: RedisService,
  ) {}

  async onModuleInit() {
    // this.startMigration();
    // this.migrateSpecificCards(['4e4440b7-d846-4e3e-a2ed-ba920c587919', 'abccb0af-9ebf-41c1-91a8-f1b41d3f5a04', '0122a1ad-9a6e-423b-8a84-c44c4d365107', '56afedc5-1328-44c0-9381-d82d68493d0b'])
    // const card = await this.cardRepository.findOne({
    //   where: { cardId: '2c463d6d7f054e7abdf2adab8e4f1ce8' },
    // })
    // const balance = await this.getBridgecardBalance(card);
    // console.log(balance, 'balance', card.cardId);
    // this.bridgecardService.unLoadUsdCard({
    //   card_id: '7a7f059789ef4545bc5a8003d02fe9de',
    //   transaction_reference: randomUUID(),
    //   currency: Currency.USD,
    //   amount: 100,
    // }, '100')
    // this.startMigration();
    // await this.loopCardsMidenAndTerminate(1);
    // await this.cardService.syncAllCardholderCards('42b1a913-d431-46d1-b5b6-c1965f442bff');
    // await this.checkCustomer();
    // await this.updatedReferenceWithId();
    // await this.updateFees();
  }

  async updateFees() {
    // for (const t of trasactions) {
    //   console.log(t.id, t.fee);
    //  if(t.fee > 0) await this.transactionRepository.update({ id: t.id }, { fee: t.fee });
    // }
  }

  async updatedReferenceWithId() {
    const transactions = await this.transactionRepository.find({
      where: [
        {
          walletType: IsNull(),
          category: In([
            TransactionCategory.CREATE_CARD,
            TransactionCategory.FUND_CARD,
            TransactionCategory.WITHDRAW_CARD,
          ]),
        },
        {
          reference: IsNull(),
          category: In([
            TransactionCategory.CREATE_CARD,
            TransactionCategory.FUND_CARD,
            TransactionCategory.WITHDRAW_CARD,
          ]),
        },
      ],
    });

    for (const transaction of transactions) {
      console.log(
        transaction.id,
        'transaction',
        transaction.metadata['method'],
      );
      if (!transaction.reference) {
        transaction.reference = transaction.id;
      }
      if (
        transaction.metadata['method']
        // transaction.category == TransactionCategory.WITHDRAW_CARD
      ) {
        transaction.walletType = transaction.metadata['method']; //??
        // PaymentTransactionWalletType.CREDPAL_CASH;
      }
      await this.transactionRepository.save(transaction);
    }
  }

  async checkCustomer() {
    const duplicates = [];
    const checked = [];
    for (const user of users) {
      if (checked.includes(user.IdNumber)) {
        const u = await this.userRepository.findOne({
          where: { bvn: user.IdNumber?.toString() },
        });
        // console.log(u, 'u');
        const count = u
          ? await this.cardRepository.count({
              where: { userId: u?.id, provider: CardProvider.MIDEN },
            })
          : 0;
        duplicates.push({
          cards: users
            .filter((u) => u.IdNumber === user.IdNumber)
            .map((u) => u.NumberOfCardsIssued),

          idNumber: user.IdNumber,
          firstName: user.FirstName,
          lastName: user.LastName,
          count,
        });
        continue;
      }
      checked.push(user.IdNumber);
      // const customer = await this.midenService.getCardCustomers(user.Id);
    }
    console.log(
      duplicates.filter((e) => {
        const count = [...e.cards].reduce((acc, curr) => acc + curr, 0);
        return count > 1 && e.count != count;
      }),
      'duplicate',
    );
  }

  async loopCardsMidenAndTerminate(page = 1) {
    const data = await this.userRepository.findMany(
      {
        page,
        limit: 30,
      },
      {
        where: { midenCardHolderId: Not(IsNull()) },
        // skip,
      },
    );

    //   console.log(cards, meta);
    for (const user of data.items) {
      const cards = await this.cardRepository.find({
        where: { provider: CardProvider.MIDEN, user: { id: user.id } },
      });
      if (cards.length < 2) {
        continue;
      }
      let cardsWithoutTransactions = cards;

      const transactions = await this.transactionRepository.find({
        where: { card: { user: { id: user.id } } },
        relations: ['card'],
      });

      const transactionsMap = new Map();
      for (const transaction of transactions) {
        transactionsMap.set(transaction.card?.id, true);
        if (cards.find((card) => card.id === transaction.card?.id)) {
          if (
            cardsWithoutTransactions.find(
              (card) => card.id === transaction.card?.id,
            )
          ) {
            cardsWithoutTransactions = cardsWithoutTransactions.filter(
              (card) => card.id !== transaction.card?.id,
            );
          }
        }
      }

      for (const card of cardsWithoutTransactions) {
        console.log(card.id, 'card', card.id);
        this.cardRepository.update(
          { id: card.id },
          { isActive: false, isDeleted: true },
        );
        try {
          await this.midenService.terminateCard(card.cardId);
        } catch (error) {
          console.log(error, 'error', card.cardId);
        }
      }
    }

    if (data.meta?.totalPages > page) {
      console.log(page, 'page', data.meta.totalPages);
      await this.loopCardsMidenAndTerminate(page + 1);
    }
  }

  async loopCardsAndActivate(page = 1) {
    const { cards, meta } = await this.bridgecardService.getAllCards(page);
    //   console.log(cards, meta);
    for (const card of cards) {
      console.log(card.is_active, 'card.is_active');
      if (card.is_active !== true) {
        await this.bridgecardService.unfreezeCard(
          card.card_id,
          card.cardholder_id,
        );
        //   await this.processCard(card);
      }

      const balance = await this.bridgecardService.getCardDetailsFromBridge(
        card.card_id,
        card.cardholder_id,
      );
      console.log(balance.available_balance, 'balance', card.card_id);
      // if (balance.available_balance > 0) {
      //   try {
      //     await this.bridgecardService.unLoadUsdCard(
      //       {
      //         card_id: card.card_id,
      //         transaction_reference: randomUUID(),
      //         currency: Currency.USD,
      //         amount: balance.available_balance,
      //       },
      //       card.cardholder_id,
      //     );
      //   } catch (error) {
      //     console.log(error, 'error', card.card_id);
      //   }
      // } else {
      if (card.card_id != '0bd4d014e167418f87ba656f708cb5d1') {
        try {
          await this.bridgecardService.terminateCard(card.card_id);
        } catch (error) {
          console.log(error, 'error', card.card_id);
        }
        await this.cardRepository.update(
          {
            cardId: card.card_id,
          },
          {
            isActive: false,
            isDeleted: true,
          },
        );
      }
      // }
    }

    if (meta.pages > page) {
      console.log(page, 'page', meta.pages);
      await this.loopCardsAndActivate(page + 1);
    }
  }

  async loopThroughCards(page = 1) {
    try {
      this.logger.log(`Processing page ${page} of cards`);
      const { cards, hasMore } = await this.getCardsPage(page);

      if (cards.length === 0) {
        this.logger.log('No more cards to process');
        return;
      }

      // Process the current batch of cards
      const promises = cards.map((card) => this.processCard(card));
      await Promise.allSettled(promises);

      // Continue to the next page if there are more cards
      if (hasMore) {
        await setTimeout(() => this.loopThroughCards(page + 1), 5000);
      } else {
        this.logger.log('Card migration completed successfully');
      }
    } catch (error) {
      this.logger.error(
        `Error in loopThroughCards: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async startMigration() {
    this.logger.log('Starting card migration from Bridgecard to Miden');
    await this.loopThroughCards();
  }

  async getCardsPage(page: number) {
    const skip = (page - 1) * this.BATCH_SIZE;
    const cards = await this.cardRepository.find({
      where: { provider: CardProvider.BRIDGECARD, cardCurrency: Currency.USD },
      skip,
      take: this.BATCH_SIZE,
      relations: ['user'],
    });

    const hasMore = cards.length === this.BATCH_SIZE;
    return { cards, hasMore };
  }

  async processCard(card: Card) {
    try {
      this.logger.log(`Processing card ${card.id} for user ${card.userId}`);

      // Check if user already has a Miden card
      const existingMidenCard = await this.cardRepository.findOne({
        where: {
          userId: card.userId ?? card.user?.id,
          provider: CardProvider.MIDEN,
        },
      });

      if (existingMidenCard) {
        this.logger.log(
          `User ${card.userId} already has a Miden card. Skipping migration.`,
        );
        return;
      }

      let pendingTransaction = await this.transactionRepository.findOne({
        where: {
          reference: card.id,
          type: TransactionType.CREDIT,
        },
      });

      if (pendingTransaction) {
        this.logger.log(`Processing card ${card.id} for user ${card.userId}`);
        try {
          await this.bridgecardService.unLoadUsdCard(
            {
              card_id: card.cardId,
              transaction_reference: pendingTransaction.reference,
              currency: Currency.USD,
              amount: pendingTransaction.amount * 100,
            },
            card.userId,
          );
          await this.cardService.createCard(
            pendingTransaction.reference,
            'success',
          );
        } catch (error) {
          console.log(error);
        }

        return;
      }

      // Get current balance from Bridgecard
      const balance = await this.getBridgecardBalance(card);
      console.log(balance, 'balance', card.cardId);

      // Store balance in Redis cache
      await this.cacheCardBalance(card.cardId, balance / 100);

      // Create a PENDING transaction first
      pendingTransaction = await this.createPendingTransaction(
        card,
        Math.max(balance / 100, 1),
      );

      try {
        await this.bridgecardService.unLoadUsdCard(
          {
            card_id: card.cardId,
            transaction_reference: pendingTransaction.reference,
            currency: Currency.USD,
            amount: balance,
          },
          card.userId,
        );
        await this.cardService.createCard(
          pendingTransaction.reference,
          'success',
        );
      } catch (error) {
        console.log(error);
      }

      //   try {
      //     // Create new Miden card
      //     const newCard = await this.createMidenCard(card.userId, balance);

      //     // Update the transaction to SUCCESSFUL
      //     await this.updateTransactionToSuccessful(
      //       pendingTransaction.id,
      //       newCard.id,
      //     );

      //     // Send notification to user
      //     await this.notifyUser(card.userId, card.id, newCard.id);

      //     // Mark old card as inactive
      //     await this.deactivateOldCard(card.id);

      //     this.logger.log(
      //       `Successfully migrated card ${card.id} to Miden card ${newCard.id}`,
      //     );
      //   } catch (error) {
      //     // Update transaction to FAILED if card creation fails
      //     await this.updateTransactionToFailed(
      //       pendingTransaction.id,
      //       error.message,
      //     );
      //     throw error;
      //   }
    } catch (error) {
      this.logger.error(
        `Failed to process card ${card.id}: ${error.message}`,
        error.stack,
      );
      // Record the failed card for retry
      await this.recordFailedMigration(card.id, error.message);
    }
  }

  async getBridgecardBalance(card: Card) {
    try {
      const balanceResponse =
        await this.bridgecardService.getCardDetailsFromBridge(
          card.cardId,
          card.userId,
        );
      console.log(balanceResponse, 'balanceResponse', card.cardId);
      return parseFloat(balanceResponse.available_balance || '0');
    } catch (error) {
      this.logger.error(
        `Error getting balance for card ${card.cardId}: ${error.message}`,
      );
      throw error;
      //   return 0; // Default to 0 if unable to retrieve balance
    }
  }

  async cacheCardBalance(userId: string, balance: number) {
    const key = `${this.REDIS_BALANCE_KEY_PREFIX}${userId}`;
    await this.redisService.set(key, balance.toString(), { EX: 86400 }); // Cache for 24 hours
  }

  async createMidenCard(
    userId: string,
    initialBalance: number,
    oldCardId: string,
  ) {
    // Get user details
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    this.cardService.createCard(oldCardId, 'success');

    // Get user's address from verification service if available

    // Call Miden service to create the card
    // const midenCardResponse = await this.midenService.createCard({
    //   firstName: user.firstName,
    //   lastName: user.lastName,
    //   email: user.email,
    //   address1: user.address['address'],
    //   city: user.address['city'],
    //   state: user.address['state'],
    //   zipcode: user.address['postal_code'],
    //   country: user.address['country'],
    //   phone: user.phoneNumber,
    //   initialBalance,
    //   cardBrand: 'Mastercard',
    //   cardCurrency: Currency.USD,
    //   idNumber: user.idNumber,
    //   idType: 'BVN',
    //   customerBvn: user.bvn,
    //   clientReference: oldCardId,
    //   walletCurrency: Currency.USD,
    // });

    // return newCard;
  }

  async createPendingTransaction(oldCard: Card, amount: number) {
    const { fee, exchangeRate } =
      await this.cardService.getCreationAmountAndFee(
        Currency.USD,
        Math.max(amount, 1),
      );
    return await this.transactionRepository.save({
      user: { id: oldCard.userId },
      amount,
      fee: 1,
      provider: CardProvider.MIDEN,
      reference: oldCard.id,
      category: TransactionCategory.CREATE_CARD,
      currency: Currency.USD,
      type: TransactionType.CREDIT,
      status: TransactionStatus.PENDING,
      paymentStatus: TransactionStatus.SUCCESS,
      narration: 'Card migration from old card to new card',
      exchangeRate,
      metadata: {
        oldCardId: oldCard.id,
        oldProvider: CardProvider.BRIDGECARD,
        newProvider: CardProvider.MIDEN,
        initiatedAt: new Date().toISOString(),
      },
    });
  }

  //   async updateTransactionToSuccessful(
  //     transactionId: string,
  //     newCardId: string,
  //   ) {
  //     return this.transactionRepository.update(
  //       { id: transactionId },
  //       {
  //         cardId: newCardId, // Update to reference the new card
  //         status: 'successful',
  //         description: `Card migration from Bridgecard to Miden (${newCardId}) completed successfully`,
  //         metadata: {
  //           completedAt: new Date().toISOString(),
  //           newCardId,
  //         },
  //       },
  //     );
  //   }

  //   async updateTransactionToFailed(transactionId: string, errorMessage: string) {
  // return this.transactionRepository.update(
  //   { id: transactionId },
  //   {
  //     status: 'failed',
  //     description: `Card migration from Bridgecard to Miden failed: ${errorMessage}`,
  //     metadata: {
  //       failedAt: new Date().toISOString(),
  //       errorMessage,
  //     },
  //   },
  // );
  //   }

  async notifyUser(userId: string, oldCardId: string, newCardId: string) {
    // Send notification via RMQ
    // await this.no.publish('notifications', {
    //   userId,
    //   type: 'CARD_MIGRATION',
    //   title: 'Card Provider Change',
    //   message:
    //     'Your card has been migrated to a new provider for improved services. Your balance has been transferred to the new card.',
    //   data: {
    //     oldCardId,
    //     newCardId,
    //   },
    // });
  }

  async deactivateOldCard(cardId: string) {
    await this.cardRepository.update(
      { id: cardId },
      {
        isActive: false,
        isDeleted: true,
      },
    );
  }

  async recordFailedMigration(cardId: string, errorMessage: string) {
    // Record failed migration in the database or cache for retry
    const failureKey = `failed_migration:${cardId}`;
    await this.redisService.set(failureKey, errorMessage, { EX: 604800 }); // Cache for 7 days
  }

  async getCards() {
    // This is now implemented as getCardsPage and loopThroughCards methods
    this.logger.log('Starting migration of all Bridgecard cards to Miden');
    return this.startMigration();
  }

  // Method to migrate specific cards by their IDs
  async migrateSpecificCards(cardIds: string[]) {
    this.logger.log(`Starting migration for ${cardIds.length} specific cards`);

    for (const cardId of cardIds) {
      try {
        const card = await this.cardRepository.findOne({
          where: { id: cardId },
        });

        if (!card) {
          this.logger.warn(`Card with ID ${cardId} not found`);
          continue;
        }

        if (card.provider !== CardProvider.BRIDGECARD) {
          this.logger.warn(`Card ${cardId} is not a Bridgecard card, skipping`);
          continue;
        }

        await this.processCard(card);
      } catch (error) {
        this.logger.error(
          `Error migrating card ${cardId}: ${error.message}`,
          error.stack,
        );
      }
    }

    this.logger.log('Specific cards migration completed');
  }
}
