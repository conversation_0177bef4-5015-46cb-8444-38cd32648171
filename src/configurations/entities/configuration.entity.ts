import { BaseEntity } from 'src/config/repository/base-entity';
import { Column, Entity, Index } from 'typeorm';

@Entity({ name: 'configurations' })
@Index(['createdAt'])
export class Configuration extends BaseEntity {
  @Column({
    nullable: false,
  })
  title: string;

  @Column({
    nullable: false,
  })
  @Index()
  slug: string;

  @Column({
    nullable: false,
  })
  default: number;

  @Column()
  lastEditedBy: string;

  @Column()
  value: string;

  @Column()
  description: string;

  @Column('json')
  meta: any;
}
