import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { Configuration } from '../entities/configuration.entity';

@Injectable()
export class ConfigurationRepository extends TypeOrmRepository<Configuration> {
  constructor(private readonly dataSource: DataSource) {
    super(Configuration, dataSource.createEntityManager());
  }
}
