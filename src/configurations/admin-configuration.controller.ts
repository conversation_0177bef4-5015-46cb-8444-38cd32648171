import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiProperty,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { ConfigurationsService } from './configurations.service';
import { JwtAuthGuard } from '@crednet/authmanager';
import { ResponseMessage } from '@crednet/utils';

@ApiTags('admin configurations')
@ApiBearerAuth('JWT')
@UseGuards(JwtAuthGuard)
@Controller('admin/configurations')
export class AdminConfigurationsController {
  constructor(
    private readonly adminConfigurationService: ConfigurationsService,
  ) {}

  @Get('')
  @ApiOkResponse({ description: ' admin configurations found' })
  @ApiProperty({ description: 'list all configurations' })
  async getConfigurations() {
    return await this.adminConfigurationService.getConfigurations();
  }

  // @Post('')
  // @ApiOkResponse({
  //   description: 'Configurations fetched successfully',
  //   isArray: true,
  // })
  // @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  // @ApiInternalServerErrorResponse({ description: 'Unexpected error occurred' })
  // @ApiOperation({ summary: 'create new configurations' })
  // @ResponseMessage('Configurations created successfully')
  // async create(@Body() data: CreateConfigurationDto) {
  //   return this.adminConfigurationService.create(data);
  // }

  // @Put('')
  // @ApiOkResponse({ description: 'updated admin savings configuration found' })
  // @ApiProperty({ description: 'update admin saving configuration' })
  // async updateConfiguration(
  //   @GetAuthData() userData: AuthData,
  //   @Body() updateConfigurationRequest: UpdateConfigurationsDto,
  // ) {
  //   return await this.adminConfigurationService.updateConfiguration(
  //     updateConfigurationRequest,
  //     userData,
  //   );
  // }

  @Get('/:slug')
  @ApiOkResponse({
    description: 'Single configuration fetched successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiInternalServerErrorResponse({ description: 'Unexpected error occurred' })
  @ApiOperation({ summary: 'get a single configurations' })
  @ResponseMessage('Single configuration fetched successfully')
  async getSingleConfig(@Param('slug') slug: string) {
    return await this.adminConfigurationService.findOne(slug);
  }
}
