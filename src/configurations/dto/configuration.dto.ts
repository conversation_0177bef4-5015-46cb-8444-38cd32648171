import { ApiProperty } from '@nestjs/swagger';
import { IsDefined, IsNotEmptyObject } from 'class-validator';

export class CreateConfigurationDto {
  @ApiProperty()
  @IsDefined()
  title: string;

  @ApiProperty()
  @IsDefined()
  slug: string;

  @ApiProperty()
  options?: Array<{ title: string; value: string }>;

  @ApiProperty()
  @IsDefined()
  default: number;

  @ApiProperty()
  @IsDefined()
  value?: string;
}

export class UpdateConfigurationsDto {
  @ApiProperty({ type: Object })
  @IsNotEmptyObject()
  configurations: Record<string, string>;
}
