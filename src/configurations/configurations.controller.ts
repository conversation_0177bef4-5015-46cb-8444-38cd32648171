import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiProperty,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { ConfigurationsService } from './configurations.service';
import { JwtAuthGuard } from '@crednet/authmanager';
import { ResponseMessage } from '@crednet/utils';

@ApiTags('Configurations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@Controller('configurations')
export class ConfigurationsController {
  constructor(
    private readonly adminConfigurationService: ConfigurationsService,
  ) {}

  @Get('')
  @ApiOkResponse({ description: ' admin configurations found' })
  @ApiProperty({ description: 'list all configurations' })
  async getConfigurations() {
    return await this.adminConfigurationService.getConfigurations();
  }

  @Get('/:slug')
  @ApiOkResponse({
    description: 'Single configuration fetched successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiInternalServerErrorResponse({ description: 'Unexpected error occurred' })
  @ApiOperation({ summary: 'get a single configurations' })
  @ResponseMessage('Single configuration fetched successfully')
  async getSingleConfig(@Param('slug') slug: string) {
    return await this.adminConfigurationService.findOne(slug);
  }
}
