import { Module } from '@nestjs/common';
import { ConfigurationRepository } from './repositories/configuration.repository';
import { ConfigurationsController } from './configurations.controller';
import { ConfigurationsService } from './configurations.service';
import { AdminConfigurationsController } from './admin-configuration.controller';
import { RateRepository } from './repositories/rate.repository';

@Module({
  imports: [],
  exports: [ConfigurationsService],
  controllers: [ConfigurationsController, AdminConfigurationsController],
  providers: [ConfigurationRepository, ConfigurationsService, RateRepository],
})
export class ConfigurationsModule {}
