import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import {
  CreateConfigurationDto,
  UpdateConfigurationsDto,
} from './dto/configuration.dto';
import { ConfigurationRepository } from './repositories/configuration.repository';
import { Configuration } from './entities/configuration.entity';
import { AuthData } from '@crednet/authmanager';
import { ConfigurationKeys, Events, ExchangeCategory } from 'src/utils/enums';
import { OnEvent } from '@nestjs/event-emitter';
import { RateRepository } from './repositories/rate.repository';

@Injectable()
export class ConfigurationsService {
  constructor(
    private readonly configurationRepository: ConfigurationRepository,
    private readonly rateRepository: RateRepository,
  ) {}

  async getConfigurations() {
    try {
      return await this.configurationRepository.find({
        order: {
          createdAt: 'desc',
        },
      });
    } catch (error) {
      throw error;
    }
  }

  async create(data: CreateConfigurationDto) {
    try {
      return this.configurationRepository.save({
        lastEditedBy: '',
        description: '',
        ...data,
        options: data.options || [],
      });
    } catch (error) {
      throw error;
    }
  }

  async findMany(): Promise<Pick<Configuration, 'title' | 'value'>[]> {
    try {
      return await this.configurationRepository.find({
        select: { slug: true, title: true, value: true },
      });
    } catch (error) {
      throw error;
    }
  }

  async findOne(
    slug: string,
    defaultValue?: any,
  ): Promise<{
    value: string;
  }> {
    try {
      return (
        (await this.configurationRepository.findOne({
          where: { slug },
          select: { value: true },
        })) ?? { value: defaultValue }
      );
    } catch (error) {
      throw error;
    }
  }

  async updateConfiguration(data: UpdateConfigurationsDto, auth: AuthData) {
    try {
      const changed: Array<Configuration> = [];
      const slugs = Object.keys(data.configurations);
      const configurations = await this.configurationRepository.find({
        where: { slug: In(slugs) },
      });

      for (const slug in data.configurations) {
        if (Object.prototype.hasOwnProperty.call(data.configurations, slug)) {
          const value = data.configurations[slug];

          const configuration = configurations.find(
            (config) => config.slug === slug,
          );

          if (!configuration) continue;

          if (configuration.value !== value) {
            await this.configurationRepository.update(
              {
                id: configuration.id,
              },
              {
                value: value?.toString(),
                lastEditedBy: auth?.id + '',
              },
            );
            changed.push(
              await this.configurationRepository.findOne({
                where: {
                  id: configuration.id,
                },
              }),
            );
          }
        }
      }

      return changed;
    } catch (error) {
      throw error;
    }
  }

  async getDollarDepositExchangeRate(defaultValue: any) {
    try {
      const value = await this.getConfigurationValue(
        ConfigurationKeys.DOLLAR_DEPOSIT_EXCHANGE__RATE,
        defaultValue,
      );
      return value;
    } catch (error) {
      throw error;
    }
  }

  async getDollarWithdrawalExchangeRate(defaultValue: any) {
    try {
      return this.getConfigurationValue(
        ConfigurationKeys.DOLLAR_WITHDRAWAL_EXCHANGE__RATE,
        defaultValue,
      );
    } catch (error) {
      throw error;
    }
  }

  async getConfigurationValue(slug: string, defaultValue: any) {
    try {
      const configuration = await this.configurationRepository.findOne({
        where: {
          slug,
        },
        select: {
          value: true,
        },
      });
      return configuration ? configuration.value : defaultValue;
    } catch (error) {
      throw error;
    }
  }

  convertCurrencyUsd = async (
    amount: number | string,
    category: ExchangeCategory,
  ) => {
    try {
      const dollarExchangeRate = await this.getDollarDepositExchangeRate(1600);
      const dollarWithdrawalRate =
        await this.getDollarWithdrawalExchangeRate(1500);
      return category === ExchangeCategory.DEPOSIT
        ? Number(amount) * dollarExchangeRate
        : Number(amount) * dollarWithdrawalRate;
    } catch (error) {
      throw error;
    }
  };

  @OnEvent(Events.UPDATE_FX_RATE)
  async updateFx(payload: { buyRate: number; sellRate: number }) {
    console.log(payload);
    const funding = payload.buyRate + 50;
    const withdrawal = payload.sellRate - 50;
    console.log(payload, funding, withdrawal);

    await this.rateRepository.insert({
      funding,
      withdrawal,
    });

    await this.updateConfiguration(
      {
        configurations: {
          [ConfigurationKeys.DOLLAR_WITHDRAWAL_EXCHANGE__RATE]:
            withdrawal.toString(),
          [ConfigurationKeys.DOLLAR_DEPOSIT_EXCHANGE__RATE]: funding.toString(),
        },
      },
      {
        token_identifier: '',
        transaction_pin: '',
        email: '<EMAIL>',
        id: 1,
        full_name: 'CredPal Developers',
        last_name: 'Developer',
        name: 'CredPal',
        phone_no: '',
        pnd: 0,
      },
    );
  }
}
