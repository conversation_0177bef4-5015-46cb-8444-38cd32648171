import { Controller } from '@nestjs/common';
import { LogsService } from './logs.service';
import { CreateLogDto } from './dto/create-log.dto';
import { OnEvent } from '@nestjs/event-emitter';
import { Events } from 'src/utils/enums';

@Controller()
export class LogsController {
  constructor(private readonly logsService: LogsService) {}

  @OnEvent(Events.CREATE_LOGS)
  create(createLogDto: CreateLogDto) {
    // console.log('createLogDto', createLogDto);
    return this.logsService.create(createLogDto);
  }
}
