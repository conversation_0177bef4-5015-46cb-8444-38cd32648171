import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from 'src/config/repository/base-entity';

@Entity({ name: 'logs' })
export class Log extends BaseEntity {
  @Index('userId')
  @Column({
    nullable: false,
  })
  userId: string;

  @Index('url')
  @Column({
    nullable: true,
  })
  url: string;

  @Column({ nullable: true, type: 'json' })
  headers: any;

  @Column({
    nullable: false,
  })
  @Index('status')
  status: string;

  @Column({ nullable: true, type: 'json' })
  request: any;

  @Column({ nullable: true, type: 'json' })
  response: any;
}
