import { Injectable, OnModuleInit } from '@nestjs/common';
import { BrideCardEvents, Exchanges } from 'src/utils/enums';
import { RabbitmqService } from '@crednet/utils';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class WebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rabbitmqService: RabbitmqService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  onModuleInit() {
    for (const eventType of Object.values(BrideCardEvents)) {
      this.rabbitmqService.subscribe(
        `${Exchanges.WEBHOOK}.bridgecard.${eventType}`,
        async ({ data: payload, message, ack }) => {
          console.log({ message, payload });
          try {
            await this.callFn(eventType, payload);
            ack();
          } catch (e) {
            console.log(e);
            throw e;
          }
        },
      );
    }
  }

  async callFn(event: string, data: any) {
    await this.eventEmitter.emit(event, data);
  }
}
