import { Module } from '@nestjs/common';
import { TransactionRepository } from 'src/card/repositories/transaction.repository';
import { CardRepository } from 'src/card/repositories/card.repository';
import { CardProducer } from 'src/card/card.producer';
import { ConfigurationsModule } from 'src/configurations/configurations.module';
import { UserDataRepository } from 'src/card/repositories/user.repository';
import { WebhookConsumer } from './webhook.consumer';

@Module({
  imports: [ConfigurationsModule],
  providers: [
    TransactionRepository,
    CardRepository,
    CardProducer,
    UserDataRepository,
    WebhookConsumer,
  ],
})
export class WebhookModule {}
