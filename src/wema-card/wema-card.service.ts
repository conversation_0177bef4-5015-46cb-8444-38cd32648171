import { BadRequestException, Injectable } from '@nestjs/common';
import { WemaAccountRepository } from './repository/wema.account.repository';
import { AddressRepository } from './repository/address.repository';
import {
  CreateWemaAccountDto,
  VerifyWemaOtpDto,
} from './dtos/wema.account.dto';
import { AuthData } from '@crednet/authmanager';
import {
  WemaService,
  SecurityInfoEncryptionService,
  WemaCardResponse,
  WemaCardDetails,
  UserCardEncryptionService,
} from '@app/wema';
import { CreateResidentialAddressDto } from './dtos/address.dto';
import { Tier } from './entities/wema.account';
import { CardStatus } from './entities/wema-card.entity';
import { InitiateFundingDto } from './dtos/wema.fund.transactions';
import { WemaFundTransactionRepository } from './repository/wema.fund.transactions';
import { MoreThan } from 'typeorm';
import { randomUUID } from 'crypto';
import {
  Currency,
  PaymentCacheService,
  PaymentTransactionSource,
  QueryTransactionDto,
  RabbitmqService,
  ReverseTransactionInterface,
} from '@crednet/utils';
import { Events, Exchanges, PaymentEvents, PaymentTypes } from '../utils/enums';
import {
  FundTransaction,
  TransactionStatus,
} from './entities/wema.fund.transactions';
import * as crypto from 'crypto';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  VirtualCardRequestDto,
  ActivateVirtualCardDto,
} from './dtos/virtual.card.request.dto';
import config from '../config';
import { WemaCardRepository } from './repository/wema-card.repository';
import { WemaExternalTransactionRepository } from './repository/wema.external.transactions';

@Injectable()
export class WemaCardService {
  constructor(
    private readonly wemaAccountsRepository: WemaAccountRepository,
    private readonly wemaFundTransactionRepository: WemaFundTransactionRepository,
    private readonly addressRepository: AddressRepository,
    private readonly wemaCardRepository: WemaCardRepository,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly rmqService: RabbitmqService,
    private readonly wemaService: WemaService,
    @InjectQueue(Events.REQUERY_FUND_WEMA_CARD)
    private readonly requeryTransactionQueue: Queue,
    private readonly securityInfoEncryptionService: SecurityInfoEncryptionService,
    private readonly userCardEncryptionService: UserCardEncryptionService,
    private readonly wemaExternalTransactionRepository: WemaExternalTransactionRepository,
  ) {}

  async initiateVirtualAccountCreation(
    dto: CreateWemaAccountDto,
    auth: AuthData,
  ) {
    try {
      const existingAccount = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (existingAccount) {
        throw new BadRequestException('Account already exists');
      }

      const result = await this.wemaService.generatePartnershipAccount({
        phoneNumber: auth.phone_no,
        bvn: dto.bvn,
        nin: dto.nin,
        emailAddress: auth.email,
        residentialAddress: dto.residentialAddress,
        liveImageOfFace: dto.liveImageOfFace,
      });
      if (result.statusCode !== 200) {
        throw new BadRequestException(result.message);
      } else {
        const residentialAddress = this.addressRepository.create({
          ...dto.residentialAddress,
        });
        await this.addressRepository.save(residentialAddress);
        const wemaAccount = this.wemaAccountsRepository.create({
          userId: auth.id + '',
          bvn: dto.bvn,
          nin: dto.nin,
          email: auth.email,
          phoneNumber: auth.phone_no,
          residentialAddress,
          trackingId: result.data.trackingId,
        });
        await this.wemaAccountsRepository.save(wemaAccount);
        return wemaAccount;
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async validateOtp(dto: VerifyWemaOtpDto, auth: AuthData) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.validateOtpAndGenerateAccount({
        otp: dto.otp,
        trackingId: accountExists.trackingId,
        phoneNumber: auth.phone_no,
      });
      if (result.statusCode !== 200) {
        throw new BadRequestException(result.message);
      } else {
        return {
          status: 'success',
          message: `Hello! ${auth.name}, your account will be created shortly. If your account is not generated, please try again in ten minutes.`,
        };
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async resendOtp(auth: AuthData) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.resendOtpRequest({
        phoneNumber: auth.phone_no,
        trackingId: accountExists.trackingId,
      });
      if (result.statusCode !== 200) {
        throw new BadRequestException(result.message);
      } else {
        return {
          status: 'success',
          message: `Hi, ${auth.name}! A new Otp sent to your Phonenumber`,
        };
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async handleAccountGeneratedEvent(data: any) {
    await this.wemaAccountsRepository.update(
      { phoneNumber: data.phoneNumber },
      {
        accountNumber: data.nuban,
        accountName: data.nubanName,
      },
    );

    //todo: send notfication
  }

  async handleFailedAddressVerificationEvent(data: any) {
    console.log(data);

    //todo notify user of failure so that they can update address for verification
  }

  async updateAddressForVerification(
    auth: AuthData,
    dto: CreateResidentialAddressDto,
  ) {
    try {
      const accountExists = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!accountExists)
        throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.reSubmitPartnerAddress({
        accountNumber: accountExists.accountNumber,
        residentialAddress: dto,
      });

      if (result.statusCode !== 200) {
        throw new BadRequestException(result.message);
      }

      const residentialAddress = this.addressRepository.create({
        ...dto,
      });
      await this.addressRepository.save(residentialAddress);
      await this.wemaAccountsRepository.update(
        { userId: auth.id + '' },
        {
          residentialAddress,
        },
      );
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async handleSuccessfulAddressVerificationEvent(data: any) {
    await this.wemaAccountsRepository.update(
      { phoneNumber: data.phoneNumber },
      {
        tier: Tier.VERIFIED,
      },
    );
    //todo notify user of success
  }

  async throttleCall(userId: string, minute: number = 2) {
    // reject if successful creation txn was made at most 2 minutes ago
    const minutesAgo = new Date(Date.now() - minute * 60 * 1000); // Calculate the time 2 minutes ago and reduce by 1 hr because of timezone

    const pastTxn = await this.wemaFundTransactionRepository.findOne({
      where: {
        wemaAccounts: { userId: userId },
        createdAt: MoreThan(minutesAgo),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (pastTxn) {
      throw new BadRequestException(
        `Transaction recently called, please wait ${minute} minutes and try again`,
      );
    }
  }

  // ============= FUND TRANSACTION ============= //

  async initiateFunding(dto: InitiateFundingDto, auth: AuthData) {
    try {
      const account = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
      });

      if (!account) throw new BadRequestException('Account does not exist');

      await this.throttleCall(account.id);

      const reference = `cp-wema-${randomUUID()}`;

      const txn = await this.wemaFundTransactionRepository.save({
        wemaAccounts: account,
        amount: +dto.amount,
        narration: dto.narration,
        destinationAccountNumber: account.accountNumber,
        reference,
        userId: account.userId,
      });

      await this.paymentCacheService.savePayment({
        source: PaymentTransactionSource.CARDS_SERVICE,
        userId: account.userId,
        currency: Currency.NGN,
        reference,
        amount: dto.amount,
        description: 'virtual naira card funding',
        returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
        meta: {
          type: PaymentTypes.FUND,
          amount: +dto.amount,
          reference,
        },
      });

      return txn;
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async finalisePayment(transaction: FundTransaction) {
    await this.wemaFundTransactionRepository.update(
      { id: transaction.id },
      { paymentStatus: TransactionStatus.SUCCESS },
    );

    const securityInfo = this.securityInfoEncryptionService.encryptSecurityInfo(
      {
        amount: transaction.amount,
        destinationAccountNumber: transaction.destinationAccountNumber,
        transactionReference: transaction.reference,
        timestamp: new Date().toISOString(),
        nonce: crypto.randomBytes(8).toString('hex'),
      },
    );

    try {
      const result = await this.wemaService.fundWallet({
        amount: transaction.amount,
        destinationAccountNumber: transaction.destinationAccountNumber,
        securityInfo,
        transactionReference: transaction.reference,
        narration: transaction.narration,
        useCustomNarration: true,
      });

      if (result.errorMessages.length > 0 || result.errorMessage.length > 0) {
        await this.wemaFundTransactionRepository.update(
          { id: transaction.id },
          { errors: result as any },
        );
      } else {
        await this.wemaFundTransactionRepository.update(
          { id: transaction.id },
          { status: TransactionStatus.PROCESSING },
        );
      }
    } catch (error) {
      console.log(error);
    } finally {
      this.requeryTransactionQueue.add(
        'requery_fund_wema_card',
        {
          reference: transaction.reference,
        },
        {
          lifo: false,
          attempts: 1,
          backoff: { type: 'exponential', delay: 2000 },
          jobId: randomUUID(),
          removeOnComplete: true,
          removeOnFail: false,
          delay: 10000,
        },
      );
    }
  }

  async requery(reference: string): Promise<any> {
    const transaction = await this.wemaFundTransactionRepository.findOne({
      where: { reference: reference },
      relations: ['wema-accounts'],
    });

    if (!transaction || transaction.status == TransactionStatus.SUCCESS) {
      return;
    }

    await this.verifyTransaction(reference);
  }

  async verifyTransaction(reference: string) {
    try {
      const data = await this.wemaService.getTransactionDetails(reference);

      console.log('verifyWithdrawal ', data);

      switch (data?.result?.data.status.toLowerCase()) {
        //Todo find out conditions where a transaction is rejected,
        // todo also ask what if we check and the status is still processing
        case 'failed':
          this.handleError(
            reference,
            data,
            data?.result?.data.status.toLowerCase() == 'failed',
          );
          break;
        case 'done':
          this.handleSuccess(reference, data);
          break;

        default:
          break;
      }
    } catch (error) {
      console.error(error);
      this.handleError(reference, error);
    }
  }

  async handleError(reference: string, error: any, updateStatus = false) {
    const update = {
      errors: typeof error === 'string' ? { message: error } : error,
    };
    const transaction = await this.wemaFundTransactionRepository.findOne({
      where: {
        reference,
      },
      relations: ['wema-accounts'],
    });

    update['status'] = TransactionStatus.FAILED;
    if (updateStatus) {
      console.log('handleError ', updateStatus, transaction.reference);
      this.refundTransaction(reference);
    }
    this.wemaFundTransactionRepository.update({ reference }, update);
  }

  async handleSuccess(reference: string, data: any) {
    await this.wemaFundTransactionRepository.update(
      { reference },
      { meta: data, status: TransactionStatus.SUCCESS },
    );
  }

  async requeryProcessing(page: number) {
    console.log('running job:: requeryProcesing ', page);
    // Get funding transactions
    const fundingItems = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
      },
    );

    // Combine results
    const items = {
      items: [...fundingItems.items],
      meta: {
        totalPages: Math.max(fundingItems.meta.totalPages),
      },
    };

    for (const item of items.items) {
      console.log('running job:: requeryProcesing ', item.reference);
      try {
        await this.requery(item.reference);
      } catch (error) {
        console.log(error);
      }
    }

    if (page < items.meta.totalPages) {
      return this.requeryProcessing(++page);
    }
  }

  async handleRefund(page: number) {
    console.log('running job:: handleRefund ', page);
    const items = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 100,
      },
      {
        where: {
          status: TransactionStatus.FAILED,
          paymentStatus: TransactionStatus.SUCCESS,
          isRefunded: false,
        },
      },
    );

    for (const item of items.items) {
      console.log('running job:: handleRefund ', item.reference);

      this.refundTransaction(item.reference);
    }

    if (page < items.meta.totalPages) {
      return this.handleRefund(++page);
    }
  }

  async refundTransaction(reference: string) {
    console.log('refundTransaction ', reference);

    this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentEvents.REVERSE_TRANSACTION,
      data: {
        source: PaymentTransactionSource.CARDS_SERVICE,
        reference,
        reason: 'Transaction failed from provider',
        returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
      } as ReverseTransactionInterface,
    });
  }

  async reprocessPendingProcessing(page: number) {
    console.log('running job:: reprocessPendingProcessing ', page);
    const items = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: TransactionStatus.PENDING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
      },
    );

    for (const item of items.items) {
      await this.finalisePayment(item);
    }

    if (page < items.meta.totalPages) {
      return this.reprocessPendingProcessing(++page);
    }
  }

  async requeryPendingPayment(page: number) {
    console.log('running job:: requeryPendingPayment ', page);
    const items = await this.wemaFundTransactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          paymentStatus: TransactionStatus.PROCESSING,
        },
      },
    );

    for (const item of items.items) {
      this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.QUERY_TRANSACTION,
        data: {
          returningRoutingKey: PaymentEvents.WEMA_CARD_PAYMENT_STATUS,
          reference: item.reference,
        } as QueryTransactionDto,
      });
    }

    if (page < items.meta.totalPages) {
      return this.requeryPendingPayment(++page);
    }
  }

  async handleFundTransactionWebhook(data: any) {
    if (data.status.toLowerCase() === 'successful') {
      this.handleSuccess(data.transactionReference, data);
    } else {
      this.handleError(data.transactionReference, data);
    }
  }

  async handleCardTransactionWebhook(data: any) {
    if (data.status.toLowerCase() === 'successful') {
      this.handleSuccess(data.transactionReference, data);
    } else {
      this.handleError(data.transactionReference, data);
    }
  }

  async handleTransactionNotificationWebhook(data: any) {
    const fetchAccountBalance =
      await this.wemaService.getWalletAccountBalanceDetails(data.accountNumber);

    const account = await this.wemaAccountsRepository.findOne({
      where: {
        accountNumber: data.accountNumber,
      },
    });

    if (!account) {
      return;
    }

    await this.wemaAccountsRepository.update(
      {
        accountNumber: data.accountNumber,
      },
      { accountBalance: fetchAccountBalance.result.availableBalance },
    );

    await this.wemaExternalTransactionRepository.save({
      wemaAccounts: account,
      transactionType: data.transactionType,
      amount: data.amount,
      narration: data.narration,
    });

    // todo add notification for this
  }

  //================= VIRTUAL CARD GENERATION ====================//
  async requestVirtualCard(auth: AuthData, dto: VirtualCardRequestDto) {
    try {
      const account = await this.wemaAccountsRepository.findOne({
        where: {
          userId: auth.id + '',
        },
        relations: ['residentialAddress'],
      });

      if (!account) throw new BadRequestException('Account does not exist');

      const result = await this.wemaService.createVirtualCard({
        emailaddress: account.email,
        phoneNumber: account.phoneNumber,
        amount: dto.amount,
        accountNo: account.accountNumber,
        customerAddress: account.residentialAddress.fullAddress,
        cardKey: config.wema.cardKey,
        currency: Currency.NGN,
        CustomerState: account.residentialAddress.state,
      });

      if (result.status !== true) {
        throw new BadRequestException(result);
      } else {
        const card = this.wemaCardRepository.create({
          userId: auth.id + '',
          cmpNumber: result.data,
          wemaAccounts: account,
        });
        await this.wemaCardRepository.save(card);
        return card;
      }
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }
  }

  async handleCardCreationWebhook(data: WemaCardResponse) {
    const card = await this.wemaCardRepository.findOne({
      where: {
        cmpNumber: data.data,
      },
      relations: ['wemaAccounts'],
    });

    if (!card) {
      return;
    }

    try {
      const cardDetails: WemaCardDetails =
        await this.wemaService.getVirtualCardDetails(
          card.wemaAccounts.accountNumber,
        );

      const encryptedCardDetails =
        this.userCardEncryptionService.encryptCardDetails(
          cardDetails,
          card.wemaAccounts.userId,
          card.wemaAccounts.id,
        );

      await this.wemaCardRepository.update(
        { id: card.id },
        { encryptedCardDetails },
      );

      console.log(
        `Card details encrypted and saved for card: ${card.cmpNumber}`,
      );
    } catch (error) {
      console.error('Error processing card creation webhook:', error);
      throw error;
    }
  }

  /**
   * Activates a virtual card using the provided PIN
   * @param auth - Authentication data
   * @param dto - Activation DTO containing the PIN
   * @returns Success response
   */
  async activateVirtualCard(auth: AuthData, dto: ActivateVirtualCardDto) {
    try {
      const card = await this.wemaCardRepository.findOne({
        where: {
          userId: auth.id + '',
        },
        relations: ['wemaAccounts'],
      });

      if (!card) {
        throw new BadRequestException('No card found for this user');
      }

      if (!card.encryptedCardDetails) {
        throw new BadRequestException(
          'Card details not available. Please contact support.',
        );
      }

      const cardDetails =
        this.userCardEncryptionService.decryptCardDetails<WemaCardDetails>(
          card.encryptedCardDetails,
          card.wemaAccounts.userId,
          card.wemaAccounts.id,
        );

      const activationResult = await this.wemaService.activateCard(
        card.wemaAccounts.accountNumber,
        dto.pin,
        cardDetails.expiry,
        card.wemaAccounts.email,
        cardDetails.pan,
      );

      if (activationResult.successful) {
        await this.wemaCardRepository.update(
          { id: card.id },
          { status: CardStatus.ACTIVE },
        );

        return {
          status: 'success',
          message: 'Card activated successfully',
          data: {
            cardId: card.id,
            status: CardStatus.ACTIVE,
          },
        };
      } else {
        throw new BadRequestException(
          activationResult.message || 'Card activation failed',
        );
      }
    } catch (error) {
      console.error('Error activating virtual card:', error);
      throw new BadRequestException(
        error || 'Failed to activate card. Please try again.',
      );
    }
  }
}
