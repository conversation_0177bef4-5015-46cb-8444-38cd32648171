import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { WemaCards } from '../entities/wema-card.entity';
import { DataSource } from 'typeorm';

@Injectable()
export class WemaCardRepository extends TypeOrmRepository<WemaCards> {
  constructor(private readonly dataSource: DataSource) {
    super(WemaCards, dataSource.createEntityManager());
  }
}
