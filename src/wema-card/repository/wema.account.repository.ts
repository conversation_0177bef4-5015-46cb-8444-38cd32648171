import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { WemaAccounts } from '../entities/wema.account';

@Injectable()
export class WemaAccountRepository extends TypeOrmRepository<WemaAccounts> {
  constructor(private readonly dataSource: DataSource) {
    super(WemaAccounts, dataSource.createEntityManager());
  }
}
