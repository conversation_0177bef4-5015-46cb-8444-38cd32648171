import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { ResidentialAddress } from '../entities/address';
import { DataSource } from 'typeorm';

@Injectable()
export class AddressRepository extends TypeOrmRepository<ResidentialAddress> {
  constructor(private readonly dataSource: DataSource) {
    super(ResidentialAddress, dataSource.createEntityManager());
  }
}
