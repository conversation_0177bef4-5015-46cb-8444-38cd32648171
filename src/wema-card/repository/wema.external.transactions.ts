import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { WemaExternalTransactions } from '../entities/wema.transaction.notifications';
import { DataSource } from 'typeorm';

@Injectable()
export class WemaExternalTransactionRepository extends TypeOrmRepository<WemaExternalTransactions> {
  constructor(private readonly dataSource: DataSource) {
    super(WemaExternalTransactions, dataSource.createEntityManager());
  }
}
