import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { FundTransaction } from '../entities/wema.fund.transactions';

@Injectable()
export class WemaFundTransactionRepository extends TypeOrmRepository<FundTransaction> {
  constructor(private readonly dataSource: DataSource) {
    super(FundTransaction, dataSource.createEntityManager());
  }
}
