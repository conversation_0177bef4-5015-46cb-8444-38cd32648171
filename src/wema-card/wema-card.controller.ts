import { Controller, Post, Patch, Body, UseGuards } from '@nestjs/common';
import {
  CreateWemaAccountDto,
  VerifyWemaOtpDto,
} from './dtos/wema.account.dto';
import { CreateResidentialAddressDto } from './dtos/address.dto';
import { AuthData, GetAuthData, JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { WemaCardService } from './wema-card.service';

@Controller('wema-accounts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('WEMA Account Service')
export class WemaAccountController {
  constructor(private readonly wemaCardService: WemaCardService) {}

  @Post()
  async initiateVirtualAccountCreation(
    @GetAuthData() auth: AuthData,
    @Body() dto: CreateWemaAccountDto,
  ) {
    return this.wemaCardService.initiateVirtualAccountCreation(dto, auth);
  }

  @Post('verify-otp')
  async verifyOtp(
    @GetAuthData() auth: AuthData,
    @Body() dto: VerifyWemaOtpDto,
  ) {
    return this.wemaCardService.validateOtp(dto, auth);
  }

  @Post('resend-otp')
  async resendOtp(@GetAuthData() auth: AuthData) {
    return this.wemaCardService.resendOtp(auth);
  }

  @Patch('update-address')
  async updateAddressForVerification(
    @GetAuthData() auth: AuthData,
    @Body() dto: CreateResidentialAddressDto,
  ) {
    return this.wemaCardService.updateAddressForVerification(auth, dto);
  }
}
