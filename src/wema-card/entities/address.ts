// src/entities/ResidentialAddress.ts
import { Entity, Column, OneToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { WemaAccounts } from './wema.account';

@Entity()
export class ResidentialAddress extends BaseEntity {
  @Column()
  buildingNumber: string;

  @Column({ nullable: true })
  apartment: string;

  @Column()
  street: string;

  @Column()
  city: string;

  @Column({ nullable: true })
  town: string;

  @Column()
  state: string;

  @Column()
  lga: string;

  @Column({ nullable: true })
  lcda: string;

  @Column()
  landmark: string;

  @Column({ nullable: true })
  additionalInformation: string;

  @Column()
  country: string;

  @Column()
  fullAddress: string;

  @Column({ nullable: true })
  postalCode: string;
}
