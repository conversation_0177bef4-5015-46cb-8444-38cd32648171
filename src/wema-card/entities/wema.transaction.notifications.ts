import { BaseEntity } from '../../config/repository/base-entity';
import { WemaAccounts } from './wema.account';
import { Column, Entity, ManyToOne } from 'typeorm';

@Entity({
  name: 'wema-external-transactions',
})
export class WemaExternalTransactions extends BaseEntity {
  @ManyToOne(() => WemaAccounts, (wemaAccount) => wemaAccount.id)
  wemaAccounts: WemaAccounts;

  @Column()
  transactionType: string;

  @Column({ type: 'decimal', precision: 20, scale: 2 })
  amount: string;

  @Column()
  narration: string;
}
