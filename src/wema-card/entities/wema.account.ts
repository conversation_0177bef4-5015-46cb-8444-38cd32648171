import { Column, Entity, Index, JoinColumn, OneToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { ResidentialAddress } from './address';

export enum Tier {
  INACTIVE = 1,
  UNVERIFIED = 2,
  VERIFIED = 3,
}

@Entity({
  name: 'wema-accounts',
})
export class WemaAccounts extends BaseEntity {
  @Column()
  @Index()
  userId: string;

  @Column()
  bvn: string;

  @Column()
  nin: string;

  @Column()
  email: string;

  @Column()
  phoneNumber: string;

  @Column({ nullable: true })
  accountNumber: string;

  @Column({ nullable: true })
  accountName: string;

  @Column({ default: 0, type: 'decimal', precision: 20, scale: 2 })
  accountBalance: string;

  @Column({ default: false })
  isAddressVerified?: boolean;

  @Column()
  trackingId: string;

  @Column({
    type: 'enum',
    enum: Tier,
    default: Tier.INACTIVE,
  })
  tier: Tier;

  @OneToOne(() => ResidentialAddress, { cascade: true, eager: true })
  @JoinColumn()
  residentialAddress: ResidentialAddress;
}
