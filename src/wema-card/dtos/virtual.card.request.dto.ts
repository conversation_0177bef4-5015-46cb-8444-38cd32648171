import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, Matches, IsNotEmpty } from 'class-validator';

export class VirtualCardRequestDto {
  @IsNumber()
  @ApiProperty()
  amount: number;
}

export class ActivateVirtualCardDto {
  @ApiProperty({
    description: 'A 4-digit PIN for card activation',
    example: '1234',
    pattern: '^[0-9]{4}$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{4}$/, {
    message: 'Pin must be exactly 4 digits',
  })
  pin: string;
}
