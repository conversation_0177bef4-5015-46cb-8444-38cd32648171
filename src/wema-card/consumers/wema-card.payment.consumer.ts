import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { WemaFundTransactionRepository } from '../repository/wema.fund.transactions';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
} from '@crednet/utils';
import { Exchanges, PaymentEvents } from '../../utils/enums';
import {
  FundTransaction,
  TransactionStatus,
} from '../entities/wema.fund.transactions';

@Injectable()
export class WemaCardPaymentConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
    private readonly wemaFundTransactionRepository: WemaFundTransactionRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${PaymentEvents.WEMA_CARD_PAYMENT_STATUS}`,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      async ({ data, message, client, ack, reject }) => {
        console.log(data, data?.status, 'status');
        try {
          if (data?.status) {
            await this.handlePaymentStatus(data, ack, reject);
          }
        } catch (error) {
          console.log(error);
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw error;
        }
      },
    );
  }

  async handlePaymentStatus(
    payload: QueryTransactionResponseDto,
    ack: () => void,
    reject: () => void,
  ) {
    try {
      const { reference, status, transaction } = payload;
      let data = await this.wemaFundTransactionRepository.findOne({
        where: { reference },
      });

      if (!data) {
        data = await this.wemaFundTransactionRepository.findOne({
          where: { reference: reference },
        });

        if (!data) {
          return;
        }
      }

      if (
        (data?.paymentStatus != TransactionStatus.SUCCESS &&
          data?.paymentStatus != TransactionStatus.FAILED) ||
        (status == PaymentTransactionStatus.REVERSED && !data?.isRefunded)
      ) {
        switch (status) {
          case PaymentTransactionStatus.SUCCESSFUL:
            await this.handleSuccess(data, transaction);
            break;

          case PaymentTransactionStatus.FAILED:
            await this.handleFailed(data, transaction, payload.error);
            break;

          case PaymentTransactionStatus.REVERSED:
            await this.handleRefunded(data);
            break;

          case PaymentTransactionStatus.NOT_FOUND:
            if (
              data.paymentStatus == TransactionStatus.PENDING ||
              data.paymentStatus == TransactionStatus.PROCESSING
            ) {
              await this.handleNotFound(data, transaction);
            }
            break;

          default:
            break;
        }
      }
      ack();
    } catch (e) {
      console.log(e);
      if (String(e?.message).includes('already exist')) {
        ack();
        return;
      }

      reject();
    }
  }

  private async handleSuccess(
    data: FundTransaction,
    transaction: PaymentTransaction,
  ) {
    const update = { paymentStatus: TransactionStatus.SUCCESS };
    if (transaction.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.wemaFundTransactionRepository.update({ id: data.id }, update);

    await this.wemaCardService.finalisePayment(data);
  }

  private async handleFailed(
    data: FundTransaction,
    transaction: PaymentTransaction,
    error: object,
  ) {
    const update = {
      paymentStatus: TransactionStatus.FAILED,
      status: TransactionStatus.FAILED,
      errors: error,
    };
    if (data.walletType) {
      update['walletType'] = transaction.walletType;
    }
    await this.wemaFundTransactionRepository.update({ id: data.id }, update);

    // const template =
    //   data.category == TransactionCategory.FUND_CARD
    //     ? NotificationTemplates.CARD_FUNDING_FAILED
    //     : NotificationTemplates.CARD_WITHDRAWAL_FAILED;

    // await this.rmqService.send(Exchanges.NOTIFICATION, {
    //   key: Events.CARDS_NOTIFICATION,
    //   data: {
    //     template,
    //     userId: data.user?.userId,
    //     parameter: {
    //       amount: data.amount,
    //       reference: data.reference,
    //       wallet: data.walletType,
    //       serviceType: data.category,
    //     },
    //   } as SendNotificationPayload,
    // });
  }

  private async handleRefunded(data: FundTransaction) {
    const update = { isRefunded: true };
    if (
      data.status == TransactionStatus.PENDING ||
      data.status == TransactionStatus.PROCESSING
    ) {
      update['status'] = TransactionStatus.FAILED;
    }

    if (
      data.paymentStatus == TransactionStatus.PROCESSING ||
      data.paymentStatus == TransactionStatus.PENDING
    ) {
      update['paymentStatus'] = TransactionStatus.FAILED;
    }
    await this.wemaFundTransactionRepository.update({ id: data.id }, update);
  }

  private async handleNotFound(
    data: FundTransaction,
    transaction: PaymentTransaction,
  ) {
    const paymentWalletResponse = { ...data.meta?.paymentWalletResponse };

    if (!paymentWalletResponse[transaction.walletType]) {
      paymentWalletResponse[transaction.walletType] = transaction.status;
    }
    const meta = { ...data.meta, paymentWalletResponse };

    await this.wemaFundTransactionRepository.update({ id: data.id }, { meta });

    if (
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CREDIT] ==
        PaymentTransactionStatus.NOT_FOUND &&
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CASH] ==
        PaymentTransactionStatus.NOT_FOUND
    ) {
      const billAge = Date.now() - data.createdAt.getTime();
      const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds

      if (billAge >= tenMinutesInMs) {
        await this.wemaFundTransactionRepository.update(
          { id: data.id },
          {
            meta,
            status: TransactionStatus.ABANDONED,
            paymentStatus: TransactionStatus.ABANDONED,
          },
        );
      }
    }
  }
}
