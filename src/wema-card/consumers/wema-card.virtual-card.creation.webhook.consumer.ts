import { Injectable, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { WemaCardService } from '../wema-card.service';
import { Exchanges, WemaWebhookEvent } from '../../utils/enums';

@Injectable()
export class WemaCardVirtualCardCreationWebhookConsumer
  implements OnModuleInit
{
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.${WemaWebhookEvent.VIRTUAL_CARD_CREATION}`,
      async ({ data, ack }) => {
        try {
          await this.wemaCardService.handleCardCreationWebhook(data);
          ack();
        } catch (error) {
          console.error(error);
          throw error;
        }
      },
    );
  }
}
