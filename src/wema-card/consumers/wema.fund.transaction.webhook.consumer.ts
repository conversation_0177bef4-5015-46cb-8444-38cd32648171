import { RabbitmqService } from '@crednet/utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { WemaFundTransactionRepository } from '../repository/wema.fund.transactions';
import { Exchanges, WemaWebhookEvent } from '../../utils/enums';

@Injectable()
export class WemaFundTransactionWebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
    private readonly wemaFundTransactionRepository: WemaFundTransactionRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.${WemaWebhookEvent.CARD_TRANSACTION_STATUS}`,
      async ({ data, ack }) => {
        try {
          await this.wemaCardService.handleFundTransactionWebhook(data.data);
          ack();
        } catch (error) {
          console.error(error);
          throw error;
        }
      },
    );
  }
}
