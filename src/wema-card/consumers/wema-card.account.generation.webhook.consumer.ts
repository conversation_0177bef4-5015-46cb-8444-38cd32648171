import { RabbitmqService } from '@crednet/utils';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { WemaCardService } from '../wema-card.service';
import { Exchanges, WemaWebhookEvent } from '../../utils/enums';

@Injectable()
export class WemaCardAccountGenerationWebhookConsumer implements OnModuleInit {
  constructor(
    private readonly rabbitmqService: RabbitmqService,
    private readonly wemaCardService: WemaCardService,
  ) {}

  onModuleInit() {
    this.rabbitmqService.subscribe(
      `${Exchanges.WEBHOOK}.${WemaWebhookEvent.ACCOUNT_GENERATION}`,
      async ({ data, ack }) => {
        try {
          await this.wemaCardService.handleAccountGeneratedEvent(data.data);
          ack();
        } catch (error) {
          console.error(error);
          throw error;
        }
      },
    );
  }
}
