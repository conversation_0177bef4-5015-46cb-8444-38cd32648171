import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { VersionBuildGuard } from '@crednet/utils';
import { WemaCardService } from './wema-card.service';
import { InitiateFundingDto } from './dtos/wema.fund.transactions';
import {
  VirtualCardRequestDto,
  ActivateVirtualCardDto,
} from './dtos/virtual.card.request.dto';

@Controller('wema-cards')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('WEMA Card Service')
export class WemaCardsController {
  constructor(private readonly wemaCardService: WemaCardService) {}

  @Post('fund')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  async initiateFunding(
    @GetAuthData() auth: AuthData,
    @Body() dto: InitiateFundingDto,
  ) {
    return this.wemaCardService.initiateFunding(dto, auth);
  }

  @Post('request')
  async requestVirtualCard(
    @GetAuthData() auth: AuthData,
    @Body() dto: VirtualCardRequestDto,
  ) {
    return this.wemaCardService.requestVirtualCard(auth, dto);
  }

  @Post('activate')
  async activateVirtualCard(
    @GetAuthData() auth: AuthData,
    @Body() dto: ActivateVirtualCardDto,
  ) {
    return this.wemaCardService.activateVirtualCard(auth, dto);
  }
}
