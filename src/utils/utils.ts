import { AuthData } from '@crednet/authmanager';
import { BadRequestException } from '@nestjs/common';

export function formatPhoneNumber(phoneNumber) {
  // Check if the phone number already starts with '234'
  if (phoneNumber.startsWith('234')) {
    return phoneNumber;
  }
  // Replace the leading '0' with '234' if it doesn't already start with '234'
  if (phoneNumber.startsWith('0')) {
    return '234' + phoneNumber.slice(1);
  }
  return phoneNumber; // Return as is if no modification is needed
}

export function checkPnd(auth: AuthData) {
  if (auth.pnd == 1) {
    throw new BadRequestException(
      'You are not allowed to complete this operation, please contact support',
    );
  }
}

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
