import {
  Currency,
  RabbitmqService,
  SendNotificationPayload,
} from '@crednet/utils';
import { Injectable } from '@nestjs/common';
import { Transaction } from 'src/card/entities/transaction.entity';
import { Events, Exchanges, NotificationTemplates } from 'src/utils/enums';

@Injectable()
export class NotificationService {
  constructor(private readonly rmqService: RabbitmqService) {}

  async cardCreation(transaction: Transaction, userId: string) {
    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.CARDS_NOTIFICATION,
      data: {
        template: NotificationTemplates.CARD_CREATED,
        userId,
        parameter: {
          amount: transaction.amount?.toLocaleString('en'),

          date: transaction.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          currency: transaction.currency,
          cardCurrency: transaction.currency == Currency.NGN ? '₦' : '$',
          reference: transaction.reference,
          wallet: transaction.walletType,
        },
      } as SendNotificationPayload,
    });
  }

  async cardWithdrawal(transaction: Transaction, userId: string) {
    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.CARDS_NOTIFICATION,
      data: {
        template: NotificationTemplates.CARD_WITHDRAWAL,
        userId,
        parameter: {
          amount: transaction.amount?.toLocaleString('en'),

          date: transaction.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          currency: transaction.currency,
          cardCurrency: transaction.currency == Currency.NGN ? '₦' : '$',
          reference: transaction.reference,
          wallet: transaction.walletType,
        },
      } as SendNotificationPayload,
    });
  }

  async cardFunding(transaction: Transaction, userId: string) {
    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.CARDS_NOTIFICATION,
      data: {
        template: NotificationTemplates.CARD_FUNDING,
        userId,
        parameter: {
          amount: transaction.amount?.toLocaleString('en'),

          date: transaction.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          currency: transaction.currency,
          cardCurrency: transaction.currency == Currency.NGN ? '₦' : '$',
          reference: transaction.reference,
          wallet: transaction.walletType,
        },
      } as SendNotificationPayload,
    });
  }

  async cardPurchase(transaction: Transaction, lastFourDigits: string, userId: string) {
    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.CARDS_NOTIFICATION,
      data: {
        template: NotificationTemplates.CARD_PURCHASE,
        userId,
        parameter: {
          amount: transaction.amount?.toLocaleString('en'),
          date: transaction.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          lastFourDigits,
          merchant: transaction.narration,
          currency: transaction.currency,
          cardCurrency: transaction.currency == Currency.NGN ? '₦' : '$',
          reference: transaction.reference,
          wallet: transaction.walletType,
        },
      } as SendNotificationPayload,
    });
  }

  async cardlowBalance(userId: string) {
    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.CARDS_NOTIFICATION,
      data: {
        template: NotificationTemplates.CARD_LOW_BALANCE,
        userId,
        parameter: {},
      } as SendNotificationPayload,
    });
  }
}
