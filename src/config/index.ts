import 'dotenv/config';

export default {
  port: Number.parseInt(process.env.PORT, 10),
  env: process.env.ENV,
  baseUrl: process.env.BASE_URL || `http://localhost:${process.env.PORT}`,
  jwt: {
    secret: process.env.JWT_SECRET,
  },
  googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
  isCronEnabled: process.env.IS_CRON_ENABLED,
  redis: {
    url: process.env.REDIS_URL,
    host: process.env.REDIS_HOST,
    username: process.env.REDIS_USER,
    password: process.env.REDIS_PASS,
    port: Number(process.env.REDIS_PORT),
  },
  db: { url: process.env.DATABASE_URL },
  queue: { base_queue: process.env.RMQ_QUEUE },
  bridgeCard: {
    sk: process.env.BRIDGE_CARD_SK,
    webhook: process.env.BRIDGE_CARD_WEBHOOK_SK,
    uri: process.env.BRIDGE_CARD_URI,
    detailsUri:
      process.env.BRIDGE_CARD_DETAILS_URI ||
      'https://issuecards-api-bridgecard-co.relay.evervault.com/v1/issuing/sandbox/cards/',
    // detailsUriNaira: process.env.BRIDGE_CARD_DETAILS__NAIRA_URI ||
    //   'https://issuecards-api-bridgecard-co.relay.evervault.com/v1/issuing/sandbox/cards/',
    bearer: process.env.BRIDGE_CARD_AUTH_TOKEN,
  },
  miden: {
    clientId: process.env.MIDEN_CLIENT_ID,
    clientSecret: process.env.MIDEN_CLIENT_SECRET,
    uri: process.env.MIDEN_URI,
    uniqueKey: process.env.MIDEN_UNIQUE_KEY,
    identityEndpoint: process.env.MIDEN_IDENTITY_ENDPOINT,
  },
  verification: {
    uri: process.env.VERIFICATION_BASE_URL,
  },
  corporateLoan: {
    uri: process.env.CORPORATE_LOAN_URI,
    encryptionkey: process.env.ENCRYPTION_KEY,
  },
  rabbitMq: {
    brockers: process.env.RMQ_URL?.split(','),
    exchange: process.env.RMQ_EXCHANGE ?? 'cp_payment',
  },
  wema: {
    walletUrl: process.env.WEMA_WALLET_SERVICES_URL,
    accountCreationUrl: process.env.WEMA_ACCOUNT_CREATION_URL,
    creditWalletUrl: process.env.WEMA_CREDIT_WALLET_URL,
    encryptionKey: process.env.PARTNER_ENCRYPTION_KEY,
    privateEncryptionKey: process.env.WEMA_PRIVATE_ENCRYPTION_KEY,
    webhookUrl: process.env.WEMA_WEBHOOK_URL,
    apiKey: process.env.WEMA_X_API_KEY,
    walletServicesSubscriptionKey:
      process.env.WEMA_WALLET_SERVICES_SUBSCRIPTION_KEY,
    creditServicesSubscriptionKey:
      process.env.WEMA_CREDIT_WALLET_SUBSCRIPTION_KEY,
    accountCreationSubscriptionKey:
      process.env.WEMA_ACCOUNT_CREATION_SUBSCRIPTION_KEY,
    accessCode: process.env.WEMA_ACCESS_CODE,
    cardKey: process.env.WEMA_CARD_KEY,
    accountManagementUrl: process.env.WEMA_ACCOUNT_MANAGEMENT_URL,
  },
};
