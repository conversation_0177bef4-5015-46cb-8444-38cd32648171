import { Controller, Get, Query, UseGuards, } from '@nestjs/common';
import { AdminService } from './admin.service';
import { JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { SearchCardDto, SearchTransactionsDto, SearchUserDto } from './dto/create-admin.dto';
 

@Controller('admin')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('cards')
  cards(@Query() dto: SearchCardDto) {
    return this.adminService.findCards(dto);
  }

  @Get('users')
  users(@Query() dto: SearchUserDto) {
    return this.adminService.findUsers(dto);
  }

  @Get('transactions')
  transactions(@Query() dto: SearchTransactionsDto) {
    return this.adminService.findTransactions(dto);
  }
}

