import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { CardRepository } from 'src/card/repositories/card.repository';
import { UserDataRepository } from 'src/card/repositories/user.repository';
import { TransactionRepository } from 'src/card/repositories/transaction.repository';

@Module({
  controllers: [AdminController],
  providers: [
    AdminService,
    CardRepository,
    TransactionRepository,
    UserDataRepository,
  ],
})
export class AdminModule {}
