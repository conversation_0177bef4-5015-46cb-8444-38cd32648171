import { Injectable, Query } from '@nestjs/common';
import {
  SearchCardDto,
  SearchTransactionsDto,
  SearchUserDto,
} from './dto/create-admin.dto';
import { CardRepository } from 'src/card/repositories/card.repository';
import { UserDataRepository } from 'src/card/repositories/user.repository';
import { TransactionRepository } from 'src/card/repositories/transaction.repository';

@Injectable()
export class AdminService {
  constructor(
    private readonly cardRepository: CardRepository,
    private readonly userDataRepository: UserDataRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async findCards(@Query() dto: SearchCardDto) {
    const where = {};
    if (dto.currency) where['currency'] = dto.currency;
    if (dto.reference) where['reference'] = dto.reference;

    if (dto.userId) {
      const user = await this.userDataRepository.findOne({
        where: { id: dto.userId },
        select: ['id'],
      });

      if (user) where['user'] = { id: user.id };
    }

    return await this.cardRepository.findMany(
      {
        limit: dto.limit ?? 30,
        page: dto.page,
      },
      {
        order: { [dto.sortColumn ?? 'createdAt']: dto.sortOrder ?? 'DESC' },
        where,
        select: { cardNumber: false, cvv: false, cardPin: false },
        relations: ['user'],
      },
    );
  }

  async findUsers(@Query() dto: SearchUserDto) {
    const where = {};
    if (dto.userId) where['userId'] = dto.userId;

    return await this.userDataRepository.findMany(
      {
        limit: dto.limit ?? 30,
        page: dto.page,
      },
      {
        order: { [dto.sortColumn ?? 'createdAt']: dto.sortOrder ?? 'DESC' },
        where,
        relations: [],
      },
    );
  }

  async findTransactions(@Query() dto: SearchTransactionsDto) {
    const where = {};
    if (dto.status) where['status'] = dto.status;
    if (dto.paymentStatus) where['paymentStatus'] = dto.paymentStatus;
    if (dto.category) where['category'] = dto.category;
    if (dto.currency) where['currency'] = dto.currency;
    if (dto.reference) where['reference'] = dto.reference;

    if (dto.userId) {
      const user = await this.userDataRepository.findOne({
        where: { id: dto.userId },
        select: ['id'],
      });

      if (user) where['user'] = { id: user.id };
    }

    return await this.transactionRepository.findMany(
      {
        limit: dto.limit ?? 30,
        page: dto.page,
      },
      {
        order: { [dto.sortColumn ?? 'createdAt']: dto.sortOrder ?? 'DESC' },
        where,
        relations: ['user'],
      },
    );
  }
}
