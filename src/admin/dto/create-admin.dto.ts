import { Currency, PaginationQueryDto } from '@crednet/utils';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import {
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';

export class SearchCardDto extends PaginationQueryDto {
  @ApiProperty({ nullable: true, required: false, enum: Currency })
  @IsOptional()
  currency: Currency;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  reference: string;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  userId: string;
}

export class SearchTransactionsDto extends PaginationQueryDto {
  @ApiProperty({ nullable: true, required: false, enum: TransactionStatus })
  //   @IsEnum(BillStatus)
  @IsOptional()
  status: TransactionStatus;
  @ApiProperty({ nullable: true, required: false, enum: TransactionStatus })
  @IsOptional()
  paymentStatus: TransactionStatus;

  @ApiProperty({ nullable: true, required: false, enum: TransactionCategory })
  @IsOptional()
  category: TransactionCategory;

  @ApiProperty({ nullable: true, required: false, enum: TransactionType })
  @IsOptional()
  type: TransactionType;

  @ApiProperty({ nullable: true, required: false, enum: Currency })
  @IsOptional()
  currency: Currency;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  reference: string;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  userId: string;
}

export class SearchUserDto extends PaginationQueryDto {
  @ApiProperty({ nullable: true, required: false, enum: Currency })
  @IsOptional()
  currency: Currency;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  firstName: string;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  lastName: string;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  userId: string;
}
