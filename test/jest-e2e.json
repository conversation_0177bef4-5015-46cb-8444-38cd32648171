{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"@app/bridgecard/(.*)": "<rootDir>/../libs/bridgecard/src/$1", "@app/bridgecard": "<rootDir>/../libs/bridgecard/src", "@app/miden/(.*)": "<rootDir>/../libs/miden/src/$1", "@app/miden": "<rootDir>/../libs/miden/src", "@app/verification/(.*)": "<rootDir>/../libs/verification/src/$1", "@app/verification": "<rootDir>/../libs/verification/src", "@app/geolocation/(.*)": "<rootDir>/../libs/geolocation/src/$1", "@app/geolocation": "<rootDir>/../libs/geolocation/src"}}