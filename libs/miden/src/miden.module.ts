import { Module } from '@nestjs/common';
import { MidenService } from './miden.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: `${config.miden.uri}/miden/api/v1/`,
      headers: {
        // token: `Bearer ${config.miden.bearer}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        UniqueKey: config.miden.uniqueKey,
      },
    }),
  ],
  providers: [MidenService],
  exports: [MidenService],
})
export class MidenModule {}
