import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import config from 'src/config';
import { catchError, firstValueFrom } from 'rxjs';
import {
  CreateCardDto,
  MidenCard,
  MidenCardCustomersResponse,
  MidenCardDetailsResponse,
  MidenCardResponse,
  MidenCardTransactionResponse,
  MidenFreezeUnfreezeCardResponse,
  MidenFxRateResponse,
  MidenResponse,
  MidenTokenResponse,
  MidenTopUpResponse,
} from './dto';
import { RedisService } from '@crednet/utils';

@Injectable()
export class MidenService {
  constructor(
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
    private readonly redisService: RedisService,
  ) {
    //  setTimeout(() => {
    //   // this.getToken();
    //  }, 10000);
  }

  private readonly TOKEN_KEY = 'miden_token';

  private nonce(reference?: string) {
    return (reference ?? crypto.randomUUID())
      .replace(/-/g, '')
      .substring(0, 20);
  }

  private textToHash(text) {
    const hash = crypto.createHash('sha1'); // Create SHA-1 hash object
    hash.update(text, 'utf8'); // Update the hash with the text encoded in UTF-8
    const hashHex = hash.digest('hex'); // Get the resulting hash as a hexadecimal string
    return hashHex;
  }
  private toBase64(input) {
    return Buffer.from(input).toString('base64');
  }

  //   The signature is a more complex computation used for authentication.
  // It is a combination of the Http verb, encoded url, client generated reference,
  //  client id, and client secrete. All will be hashed and encoded with base 64.
  private async generateSignature(
    url: string,
    method: string,
    reference: string,
  ) {
    // console.log(this.httpService.axiosRef.defaults.baseURL);
    const encodeUrl = encodeURIComponent(
      `${this.httpService.axiosRef.defaults.baseURL}${url}`,
    );
    // console.log(
    //   `${this.httpService.axiosRef.defaults.baseURL}${url}`,
    //   encodeUrl,
    // );
    // const reference =
    const clientId = config.miden.clientId;
    const clientSecret = config.miden.clientSecret;
    const baseStringToBeSigned = `${method}&${encodeUrl}&${reference}&${clientId}&${clientSecret}`;
    const hashValue = this.textToHash(baseStringToBeSigned);
    const signature = this.toBase64(hashValue);
    return signature;
  }

  async buildHeaders(url: string, method: string, reference: string) {
    // console.log('building headers', this.redisService.getClient());
    const Signature = await this.generateSignature(url, method, reference);
    const accessToken = await this.redisService.get(this.TOKEN_KEY);
    return {
      MidenAuthorization: 'MidenAuth ' + btoa(config.miden.clientId),
      Reference: reference,
      Signature,
      Authorization: `Bearer ${accessToken}`,
    };
  }

  async getToken() {
    const { data }: { data: MidenTokenResponse } = await firstValueFrom(
      this.httpService
        .post('', 'grant_type=client_credentials&scope=CardAPIs', {
          baseURL: config.miden.identityEndpoint,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Basic ${btoa(config.miden.clientId + ':' + config.miden.clientSecret)}`,
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;
            console.log(response);
            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);
    const expiryDate = new Date();
    expiryDate.setSeconds(expiryDate.getSeconds() + data.expires_in);
    await this.redisService.set(this.TOKEN_KEY, data.access_token, {
      PX: expiryDate.getTime(),
    });
  }

  async fxRate(): Promise<MidenFxRateResponse> {
    const body = {};
    const { data }: { data: MidenResponse } = await firstValueFrom(
      this.httpService
        .get(`swap/get-rate?currency=NGN&currency=USD`, {
          headers: await this.buildHeaders(
            `swap/get-rate?currency=NGN&currency=USD`,
            'GET',
            this.nonce(),
          ),
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data?.data as MidenFxRateResponse;
  }

  async createCard(body: CreateCardDto): Promise<MidenCardResponse> {
    console.log('createCard::', body);
    const { data }: { data: MidenResponse } = await firstValueFrom(
      this.httpService
        .post(`cards/issue`, body, {
          headers: await this.buildHeaders(
            'cards/issue',
            'POST',
            this.nonce(body.clientReference),
          ),
        })
        .pipe(
          catchError((error) => {
            console.log(error.response);
            console.log(error.response?.data);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);

    return data as MidenCardResponse;
  }


  async getCardDetailsMaskedPan(id: string): Promise<MidenCard | null> {
    const url = `cards/all?cardId=${id}`;
    const { data } = await firstValueFrom(
      this.httpService
        .get(url, {
          headers: await this.buildHeaders(url, 'GET', this.nonce()),
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);

    return data?.cards?.length > 0 ? data?.cards[0] : null;
  }

  async getCardDetails(id: string): Promise<MidenCardDetailsResponse> {
    const url = `cards/details/${id}/true`;
    const { data } = await firstValueFrom(
      this.httpService
        .get(url, {
          headers: await this.buildHeaders(url, 'GET', this.nonce()),
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);

    return data as MidenCardDetailsResponse;
  }

  async getCardCustomers(
    customerId?: string,
    email?: string,
  ): Promise<MidenCardCustomersResponse> {
    const url = customerId
      ? `cards/customers?customerId=${customerId}`
      : `cards/customers?email=${email}`;
    const { data } = await firstValueFrom(
      this.httpService
        .get(url, {
          headers: await this.buildHeaders(url, 'GET', this.nonce()),
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenCardCustomersResponse;
  }

  async freezeUnfreezeCard(
    cardId: string,
    shouldActivate: boolean,
  ): Promise<MidenFreezeUnfreezeCardResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
          `cards/activate-deactivate`,
          {
            cardId,
            activated: shouldActivate,
          },
          {
            headers: await this.buildHeaders(
              'cards/activate-deactivate',
              'PATCH',
              this.nonce(),
            ),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data?.data as MidenFreezeUnfreezeCardResponse;
  }


  async enableDisableAirlinePayment(
    cardId: string,
    shouldEnable: boolean,
  ): Promise<any> {
    const url = `cards/enable-airtlines/${cardId}/${shouldEnable}`;
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
         url,
          {
            cardId,
          },
          {
            headers: await this.buildHeaders(
              url,
              'PATCH',
              this.nonce(),
            ),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);

    return data?.data ;
  }

  async topUp(
    cardId: string,
    amount: number,
    reference: string,
    currency: string,
  ): Promise<MidenTopUpResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
          `cards/topup`,
          {
            cardId,
            amount,
            clientReference: reference,
            walletCurrency: currency,
          },
          {
            headers: await this.buildHeaders(
              'cards/topup',
              'PATCH',
              this.nonce(reference),
            ),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenTopUpResponse;
  }

  async withdraw(
    cardId: string,
    amount: number,
    reference: string,
    currency: string,
  ): Promise<MidenTopUpResponse> {
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
          `cards/withdrawal`,
          {
            cardId,
            amount,
            clientReference: reference,
            walletCurrency: currency,
          },
          {
            headers: await this.buildHeaders(
              'cards/withdrawal',
              'PATCH',
              this.nonce(reference),
            ),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenTopUpResponse;
  }

  async cardTransaction(
    reference: string,
  ): Promise<MidenCardTransactionResponse> {
    const url = `cards/transactions-status/${reference}`;
    const { data } = await firstValueFrom(
      this.httpService
        .get(
          url,

          {
            headers: await this.buildHeaders(url, 'GET', this.nonce()),
          },
        )
        .pipe(
          catchError((error) => {
            // console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenCardTransactionResponse;
  }

  async terminateCard(cardId: string): Promise<MidenCardTransactionResponse> {
    const url = `cards/terminate/${cardId}`;
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
          url,
          {},

          {
            headers: await this.buildHeaders(url, 'PATCH', this.nonce()),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenCardTransactionResponse;
  }

  async reIssueCard(payload: {
    cardCustomerId: string;
    initialBalance: number;
    cardBrand: string;
    nameOnCard: string;
    clientReference: string;
    walletCurrency: string;
  }): Promise<MidenCardTransactionResponse> {
    const url = `cards/re-issue`;
    const { data } = await firstValueFrom(
      this.httpService
        .patch(
          url,
          {},

          {
            headers: await this.buildHeaders(url, 'PATCH', this.nonce()),
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);

    return data as MidenCardTransactionResponse;
  }
}
