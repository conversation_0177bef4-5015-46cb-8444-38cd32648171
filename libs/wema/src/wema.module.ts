// wema.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { WemaService } from './wema.service';
import { WemaEncryptionService } from './encryption.service';
import config from 'src/config';
import { SecurityInfoEncryptionService } from './security.info.encryption.service';
import { UserCardEncryptionService } from './user-card-encryption.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      // Set only truly common headers here.
      // baseURL should not be set here as it varies.
      headers: {
        Accept: 'application/json',
        'x-api-key': config.wema.apiKey, // Common API key
        'Cache-Control': 'no-cache', // Common cache policy
      },
    }),
  ],
  providers: [
    WemaService,
    WemaEncryptionService,
    SecurityInfoEncryptionService,
    UserCardEncryptionService,
  ],
  exports: [
    WemaService,
    WemaEncryptionService,
    SecurityInfoEncryptionService,
    UserCardEncryptionService,
  ],
})
export class WemaModule {}
