import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import config from 'src/config';

@Injectable()
export class WemaEncryptionService {
  private readonly _key: Buffer;
  private readonly _iv: Buffer;

  constructor() {
    const key = config.wema.encryptionKey;

    const sha = crypto.createHash('sha256');
    this._key = sha.update(Buffer.from(key, 'utf8')).digest();

    const ivSha = crypto.createHash('sha256');
    this._iv = ivSha.update(Buffer.from(key + '_iv', 'utf8')).digest();

    if (this._iv.length > 16) {
      this._iv = Buffer.from(this._iv.subarray(0, 16));
    }
  }

  encryptObject<T>(obj: T): string {
    const jsonData = JSON.stringify(obj);

    const cipher = crypto.createCipheriv('aes-256-cbc', this._key, this._iv);
    let encrypted = cipher.update(jsonData, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    return encrypted;
  }

  decryptObject<T>(encryptedData: string): T {
    try {
      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        this._key,
        this._iv,
      );
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return JSON.parse(decrypted) as T;
    } catch (error) {
      console.error('Error decrypting object:', error);
      throw new Error('Failed to decrypt data');
    }
  }
}
