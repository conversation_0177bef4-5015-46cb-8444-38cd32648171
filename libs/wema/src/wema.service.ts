import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import {
  AccountValidationResponse,
  ActivateCardDto,
  ActivateCardResponse,
  CreateVirtualCardDto,
  FundWalletDto,
  FundWalletResponse,
  TransactionHistoryQuery,
  TransactionResponse,
  WalletInfoResponse,
  WemaCardDetails,
  WemaCardDetailsResponse,
  WemaCardResponse,
} from './dto';
import { WemaEncryptionService } from './encryption.service';
import config from 'src/config';
import {
  GeneratePartnershipAccountDto,
  GeneratePartnershipAccountResponse,
  ValidateOtpAndGenerateAccountDto,
  ValidateOtpAndGenerateAccountResponse,
  ResendOtpRequestDto,
  ResendOtpRequestResponse,
  GetPartnershipAccountDetailsResponse,
  SubmitPartnerAddressDto,
  SubmitPartnerAddressResponse,
} from './account.dto';
import { AxiosRequestConfig } from 'axios';

@Injectable()
export class WemaService {
  private readonly walletUrl: string;
  private readonly accountCreationUrl: string;
  private readonly creditWalletUrl: string;
  private readonly accountManagementUrl: string;
  private readonly logger = new Logger(WemaService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly encryptionService: WemaEncryptionService,
  ) {
    this.walletUrl = config.wema.walletUrl;
    this.accountCreationUrl = config.wema.accountCreationUrl;
    this.creditWalletUrl = config.wema.creditWalletUrl;
    this.accountManagementUrl = config.wema.accountManagementUrl;
  }

  async createVirtualCard(
    body: CreateVirtualCardDto,
  ): Promise<WemaCardResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.walletServicesSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.walletUrl}/api/Partner/partnerCard/virtualCard`,
            body,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error creating virtual card: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      return data;
    } catch (error) {
      this.logger.error(
        `Failed to create virtual card: ${error.message || error}`,
      );
      throw error;
    }
  }

  async getVirtualCardDetails(accountNo: string): Promise<WemaCardDetails> {
    this.logger.log(`Getting virtual card details for account: ${accountNo}`);

    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.walletServicesSubscriptionKey,
        },
      };
      const { data }: { data: WemaCardDetailsResponse } = await firstValueFrom(
        this.httpService
          .get(
            `${this.walletUrl}/api/Partner/partnerCard/virtual-card-details/${accountNo}`,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting virtual card details: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      if (data.status && data.data) {
        this.logger.log(
          'Successfully retrieved encrypted card details, decrypting...',
        );
        return this.encryptionService.decryptObject<WemaCardDetails>(data.data);
      }

      this.logger.error(`Failed to get card details: ${data.message}`);
      throw new Error(data.message || 'Failed to get card details');
    } catch (error) {
      this.logger.error(
        `Failed to get virtual card details: ${error.message || error}`,
      );
      throw error;
    }
  }

  async activateCard(
    accountNumber: string,
    newPin: string,
    expiryDate: string,
    emailAddress: string,
    fullPan: string,
  ): Promise<ActivateCardResponse> {
    this.logger.log(`Activating card for account: ${accountNumber}`);

    try {
      const activateCardDto: ActivateCardDto = {
        accountNumber,
        newPin,
        expiryDate,
        emailAddress,
        fullPan,
      };
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.walletServicesSubscriptionKey,
        },
      };

      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.walletUrl}/api/Partner/partnerCard/activateCard`,
            activateCardDto,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error activating card: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`Card activation response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to activate card: ${error.message || error}`);
      throw error;
    }
  }

  // Account Management APIs

  async generatePartnershipAccount(
    accountData: GeneratePartnershipAccountDto,
  ): Promise<GeneratePartnershipAccountResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.accountCreationSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.accountCreationUrl}/api/CustomerAccount/GeneratePartnershipAccount`,
            accountData,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error generating partnership account: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`Account generation response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(
        `Failed to generate partnership account: ${error.message || error}`,
      );
      throw error;
    }
  }

  async validateOtpAndGenerateAccount(
    validateData: ValidateOtpAndGenerateAccountDto,
  ): Promise<ValidateOtpAndGenerateAccountResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.accountCreationSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.accountCreationUrl}/api/CustomerAccount/ValidateOtpAndGenerateAccount`,
            validateData,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error validating OTP: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`OTP validation response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to validate OTP: ${error.message || error}`);
      throw error;
    }
  }

  async resendOtpRequest(
    resendData: ResendOtpRequestDto,
  ): Promise<ResendOtpRequestResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.accountCreationSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.accountCreationUrl}/api/CustomerAccount/ResendOtpRequest/ResendOtp`,
            resendData,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error resending OTP: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`Resend OTP response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to resend OTP: ${error.message || error}`);
      throw error;
    }
  }

  async getPartnershipAccountDetails(
    accountNumber: string,
    partnerCode: string,
  ): Promise<GetPartnershipAccountDetailsResponse> {
    this.logger.log(`Getting account details for: ${accountNumber}`);

    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.accountCreationSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .get(
            `${this.accountCreationUrl}/api/CustomerAccount/GetPartnershipAccountDetails?accountNumber=${accountNumber}&partnerCode=${partnerCode}`,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting account details: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`Account details response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(
        `Failed to get account details: ${error.message || error}`,
      );
      throw error;
    }
  }

  async reSubmitPartnerAddress(
    addressData: SubmitPartnerAddressDto,
  ): Promise<SubmitPartnerAddressResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key':
            config.wema.accountCreationSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.accountCreationUrl}/api/CustomerAccount/SubmitPartnerAddress`,
            addressData,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error submitting address: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      this.logger.log(`Address submission response: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to submit address: ${error.message || error}`);
      throw error;
    }
  }

  async getDestinationAccountWallet(
    accountNumber: string,
  ): Promise<AccountValidationResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.creditServicesSubscriptionKey,
          access: config.wema.accessCode,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .get(
            `${this.creditWalletUrl}/api/Shared/AccountNameEnquiry/Wallet/${accountNumber}`,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting destination account wallet: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      return data;
    } catch (error) {
      this.logger.error(
        `Failed to get destination account wallet: ${error.message || error}`,
      );
      throw error;
    }
  }

  async fundWallet(body: FundWalletDto): Promise<FundWalletResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'application/json-patch+json',
          'Ocp-Apim-Subscription-Key':
            config.wema.creditServicesSubscriptionKey,
          access: config.wema.accessCode,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.creditWalletUrl}/api/Shared/FundWallet`,
            body,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error funding wallet: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Failed to fund wallet: ${error.message || error}`);
      throw error;
    }
  }

  async getTransactionDetails(
    transactionReference: string,
  ): Promise<TransactionResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.creditServicesSubscriptionKey,
          access: config.wema.accessCode,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .get(
            `${this.creditWalletUrl}/api/IntraBankTransfer/ConfirmClientTransferStatus/${transactionReference}`,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting transaction details: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );

      return data;
    } catch (error) {
      this.logger.error(
        `Failed to get transaction details: ${error.message || error}`,
      );
      throw error;
    }
  }

  async getWalletAccountBalanceDetails(
    acccountNumber: string,
  ): Promise<WalletInfoResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.creditServicesSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .get(
            `${this.accountManagementUrl}/api/AccountMaintenance/CustomerAccount/GetAccountV2/accountNumber/${acccountNumber}`,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting transaction details: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );
      return data;
    } catch (error) {
      this.logger.error(
        `Failed to get transaction details: ${error.message || error}`,
      );
      throw error;
    }
  }

  async gettransactionHistory(
    body: TransactionHistoryQuery,
  ): Promise<WalletInfoResponse> {
    try {
      const requestConfig: AxiosRequestConfig = {
        headers: {
          'Ocp-Apim-Subscription-Key':
            config.wema.creditServicesSubscriptionKey,
        },
      };
      const { data } = await firstValueFrom(
        this.httpService
          .post(
            `${this.accountManagementUrl}/api/AccountMaintenance/CustomerAccount/transhistoryV2`,
            body,
            requestConfig,
          )
          .pipe(
            catchError((error) => {
              this.logger.error(
                `Error getting transaction details: ${error.response?.data || error.message}`,
              );
              throw error.response?.data || error;
            }),
          ),
      );
      return data;
    } catch (error) {
      this.logger.error(
        `Failed to get transaction details: ${error.message || error}`,
      );
      throw error;
    }
  }
}
