import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import config from 'src/config';

@Injectable()
export class UserCardEncryptionService {
  private readonly algorithm = 'aes-256-cbc';

  /**
   * Creates a unique encryption key for a user based on userId and wemaAccount.id
   * @param userId - The user's ID
   * @param wemaAccountId - The wema account ID
   * @returns Buffer containing the encryption key
   */
  private createUserKey(userId: string, wemaAccountId: string): Buffer {
    const combinedKey = `${userId}${wemaAccountId}${config.wema.encryptionKey}`;
    return crypto.createHash('sha256').update(combinedKey, 'utf8').digest();
  }

  /**
   * Creates a deterministic IV based on the user key for consistent encryption/decryption
   * @param userKey - The user's encryption key
   * @returns Buffer containing the IV
   */
  private createUserIV(userKey: Buffer): Buffer {
    const ivHash = crypto.createHash('sha256').update(userKey).digest();
    return ivHash.subarray(0, 16); // AES-256-CBC requires 16-byte IV
  }

  /**
   * Encrypts card details for a specific user
   * @param cardDetails - The card details object to encrypt
   * @param userId - The user's ID
   * @param wemaAccountId - The wema account ID
   * @returns Encrypted string
   */
  encryptCardDetails<T>(
    cardDetails: T,
    userId: string,
    wemaAccountId: string,
  ): string {
    try {
      const userKey = this.createUserKey(userId, wemaAccountId);
      const iv = this.createUserIV(userKey);

      const jsonData = JSON.stringify(cardDetails);
      const cipher = crypto.createCipheriv(this.algorithm, userKey, iv);

      let encrypted = cipher.update(jsonData, 'utf8', 'base64');
      encrypted += cipher.final('base64');

      return encrypted;
    } catch (error) {
      console.error('Error encrypting card details:', error);
      throw new Error('Failed to encrypt card details');
    }
  }

  /**
   * Decrypts card details for a specific user
   * @param encryptedData - The encrypted string
   * @param userId - The user's ID
   * @param wemaAccountId - The wema account ID
   * @returns Decrypted card details object
   */
  decryptCardDetails<T>(
    encryptedData: string,
    userId: string,
    wemaAccountId: string,
  ): T {
    try {
      const userKey = this.createUserKey(userId, wemaAccountId);
      const iv = this.createUserIV(userKey);

      const decipher = crypto.createDecipheriv(this.algorithm, userKey, iv);

      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return JSON.parse(decrypted) as T;
    } catch (error) {
      console.error('Error decrypting card details:', error);
      throw new Error('Failed to decrypt card details');
    }
  }
}
