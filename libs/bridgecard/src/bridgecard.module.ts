import { Module } from '@nestjs/common';
import { BridgecardService } from './bridgecard.service';
import config from 'src/config';
import { HttpModule } from '@nestjs/axios';
import { EncryptionService } from './encryption.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.bridgeCard.uri,
      headers: {
        token: `Bearer ${config.bridgeCard.bearer}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [BridgecardService, EncryptionService],
  exports: [BridgecardService, EncryptionService],
})
export class BridgecardModule {}
