import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { CreateCardholderDto } from './dto/create-cardholder.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Events } from 'src/utils/enums';
import { CreateLogDto } from 'src/logs1/dto/create-log.dto';
import conf from 'src/config';
import { Currency } from '@crednet/utils';

@Injectable()
export class BridgecardService {
  constructor(
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async createCardhoder(
    body: CreateCardholderDto,
    userId: string,
  ): Promise<{ cardholder_id: string }> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .post('cardholder/register_cardholder_synchronously', body)
          .pipe(
            catchError((error) => {
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });

              throw error.response?.data ?? error;
            }),
          ),
      );
    console.log(request);
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    console.log(data);
    return data?.data;
  }

  async updateCardPin(
    cardId: string,
    encryptedPin: string,
    userId: string,
  ): Promise<any> {
    const body = {
      card_id: cardId,
      card_pin: encryptedPin,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.post('cards/set_3d_secure_pin', body).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async deleteCard(cardId: string, userId: string): Promise<any> {
    const body = {
      cardId,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.delete(`cards/delete_card/${cardId}`, {}).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async getCardDetailsFromBridge(
    cardId: string,
    // currency: Currency,
    userId: string,
  ): Promise<any> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(`get_card_details?card_id=${cardId}`, {
            baseURL:
              // currency == Currency.USD
              conf.bridgeCard.detailsUri,
            // : conf.bridgeCard.detailsUriNaira,
          })
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body: { cardId },
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body: { cardId },
      config,
      headers,
      status,
      statusText,
      data: {},
      path: request.path,
      userId,
    });

    return data.data;
  }

  async getCardTransactionStatus(
    cardId: string,
    transactionId: string,
    userId: string,
  ): Promise<any> {
    const body = {
      cardId,
      transactionId,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(
            `cards/get_card_transaction_status?card_id=${cardId}&client_transaction_reference=${transactionId}`,
          )
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data;
  }

  async fxRate(): Promise<any> {
    const body = {};
    const { data } = await firstValueFrom(
      this.httpService.get(`cards/fx-rate`).pipe(
        catchError((error) => {
          console.log(error);
          const { response } = error;

          this.logData({
            body,
            config: response?.config ?? null,
            headers: response?.headers ?? null,
            status: response?.status ?? null,
            statusText: response?.statusText ?? null,
            data: response?.data ?? null,
            path: response?.request.path,
            userId: '',
          });
          throw error.response?.data ?? error;
        }),
      ),
    );

    return data?.data;
  }

  async getBridgeCardTransactions(
    cardId: string,
    page: number,
    userId: string,
  ): Promise<any> {
    const body = {
      cardId,
      page,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(`cards/get_card_transactions?card_id=${cardId}&page=${page}`)
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async getAllCardholderCards(
    cardHolderId: string,
    userId: string,
  ): Promise<any> {
    const body = {
      cardHolderId,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(`cards/get_all_cardholder_cards?cardholder_id=${cardHolderId}`)
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async getCardTransactionsById(
    cardId: string,
    reference: string,
    userId: string,
  ): Promise<any> {
    const body = {
      cardId,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(
            `cards/get_card_transaction_by_id?card_id=${cardId}&client_transaction_reference=${reference}`,
          )
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data;
  }
  async getCardTransactions(
    cardId: string,
    currency: Currency,
    userId: string,
  ): Promise<any> {
    const body = {
      cardId,
    };
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(
            currency == Currency.USD
              ? `cards/get_card_transaction?card_id=${cardId}`
              : `naira_cards/get_naira_card_transactions?card_id=${cardId}`,
          )
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async fundNgnCard(
    cardId: string,
    amount: number,
    transaction_reference: string,
    currency: Currency,
    userId: string,
  ): Promise<any> {
    const body = {
      card_id: cardId,
      amount: amount,
      transaction_reference,
      currency,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.post(`naira_cards/fund_naira_card`, body).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }
  async fundUsdCard(
    cardId: string,
    amount: number,
    transaction_reference: string,
    currency: Currency,
    userId: string,
  ): Promise<any> {
    const body = {
      card_id: cardId,
      amount: amount,
      transaction_reference,
      currency,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.patch(`cards/fund_card_asynchronously`, body).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async unloadNgnCard(body: object, userId: string): Promise<any> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.patch(`naira_cards/unload_naira_card`, body).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }
  async unLoadUsdCard(body: object, userId: string): Promise<any> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.patch(`cards/unload_card_asynchronously`, body).pipe(
          catchError((error) => {
            console.log(error.response?.data);
            // const { response } = error;

            // this.logData({
            //   body,
            //   config: response?.config ?? null,
            //   headers: response?.headers ?? null,
            //   status: response?.status ?? null,
            //   statusText: response?.statusText ?? null,
            //   data: response?.data ?? null,
            //   path: response?.request.path,
            //   userId,
            // });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async createCard(body: object, userId: string): Promise<any> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.post(`cards/create_card`, body).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async activatePhysicalCards(body: object, userId: string): Promise<any> {
    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.post(`cards/activate_physical_card`, {}).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async sendOtp(cardId: string, amount: number, userId: string): Promise<any> {
    const body = {
      cardId,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(
            `naira_cards/get_otp_message?card_id=${cardId}&amount=${amount}`,
            {},
          )
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }
  async getCardBalance(cardId: string, userId: string): Promise<any> {
    const body = {
      cardId,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .get(`cards/get_card_balance?card_id=${cardId}`, {})
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async freezeCard(cardId: string, userId: string): Promise<any> {
    const body = {
      cardId,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService.patch(`cards/freeze_card/?card_id=${cardId}`, {}).pipe(
          catchError((error) => {
            console.log(error);
            const { response } = error;

            this.logData({
              body,
              config: response?.config ?? null,
              headers: response?.headers ?? null,
              status: response?.status ?? null,
              statusText: response?.statusText ?? null,
              data: response?.data ?? null,
              path: response?.request.path,
              userId,
            });
            throw error.response?.data ?? error;
          }),
        ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  async unfreezeCard(cardId: string, userId: string): Promise<any> {
    const body = {
      cardId,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .patch(`cards/unfreeze_card/?card_id=${cardId}`, {})
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              this.logData({
                body,
                config: response?.config ?? null,
                headers: response?.headers ?? null,
                status: response?.status ?? null,
                statusText: response?.statusText ?? null,
                data: response?.data ?? null,
                path: response?.request.path,
                userId,
              });
              throw error.response?.data ?? error;
            }),
          ),
      );
    this.logData({
      body,
      config,
      headers,
      status,
      statusText,
      data,
      path: request.path,
      userId,
    });
    return data?.data;
  }

  private logData(payload: {
    userId: string;
    path: any;
    statusText: string;
    body: any;
    status: number;
    headers: any;
    data: any;
    config: any;
  }) {
    this.eventEmitter.emit(Events.CREATE_LOGS, {
      userId: payload.userId,
      url: payload.path,
      request: payload.body,
      response: {
        status: payload.status,
        statusText: payload.statusText,
        config: payload.config,
        data: payload.data,
      },
      status: payload.status + '',
      headers: payload.headers,
    } as CreateLogDto);
  }

  async getAllCards(page: number): Promise<any> {

    const { data } = await firstValueFrom(
      this.httpService.get(`cards/get_all_cards?page=${page}`).pipe(
        catchError((error) => {
          console.log(error);
          throw error;
        }),
      ),
    );

    console.log(data?.data?.cards?.length, 'cards');
    return data?.data;
  }


  async terminateCard(cardId: string): Promise<any> {
    const body = {
      cardId,
    };

    const { data, request, config, headers, status, statusText } =
      await firstValueFrom(
        this.httpService
          .delete(`cards/delete_card/${cardId}`, {})
          .pipe(
            catchError((error) => {
              console.log(error);
              const { response } = error;

              
              throw error.response?.data ?? error;
            }),
          ),
      );
  
    return data?.data;
  }

}
