interface Address {
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  house_no?: string;
}

interface Identity {
  id_type: string;
  bvn?: string;
  selfie_image?: string;
  id_image?: string;
  id_no?: string;
}

interface MetaData {
  user_id: string;
}

export interface CreateCardholderDto {
  first_name: string;
  last_name: string;
  address: Address;
  phone: string;
  email_address: string;
  identity: Identity;
  meta_data: MetaData;
}
