import { Injectable } from '@nestjs/common';
import config from 'src/config';
import { Client } from '@googlemaps/google-maps-services-js';

@Injectable()
export class GeolocationService {
  constructor() {
    // this.getPostalAddress('5 bajulaye road').then(console.log);
  }

  client = new Client({});
  API_KEY = config.googleMapsApiKey; // Replace this with your actual key

  async getPostalAddress(address) {
    try {
      const response = await this.client.geocode({
        params: {
          address,
          key: this.API_KEY,
        },
      });

      const results = response.data.results;

      if (results.length === 0) {
        console.log('No results found.');
        return null;
      }

      const formattedAddress = results[0].formatted_address;

      for (const a of results[0].address_components) {
        console.log(a);
      }

      // Extract postal code from address components
      const postalCodeComponent = results[0].address_components.find(component =>
        component.types.includes("postal_code" as any)
      );

      const streetNumber = results[0].address_components.find(component =>
        component.types.includes("street_number" as any)
      );


      const addressRoute = results[0].address_components.find(component =>
        component.types.includes("route" as any)
      );

     // Extract city (locality) and state (administrative_area_level_1)
     const cityComponent = results[0].address_components.find(component =>
      component.types.includes("locality" as any)
    );

    const stateComponent = results[0].address_components.find(component =>
      component.types.includes("administrative_area_level_1" as any)
    );

    return {
      postalCode: postalCodeComponent?.long_name,
      address: `${streetNumber?.long_name} ${addressRoute?.long_name}`,
      city: cityComponent?.long_name,
      state: stateComponent?.long_name,
    };
    } catch (error) {
      console.error('Error fetching geocoded data:', error);
      return null;
    }
  }
}
