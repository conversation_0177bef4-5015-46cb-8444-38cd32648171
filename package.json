{"name": "card-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "node dist/main", "start:dev": "nest start --watch", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "schema:sync": "yarn run typeorm schema:sync -- -d ./dist/config/data-source.js", "schema:show": "yarn run typeorm migration:show -- -d ./dist/config/data-source.js", "migration:generate": "yarn run typeorm migration:generate -d ./dist/config/data-source.js ./db/migrations/migration --timestamp", "migration:create": "yarn run typeorm migration:create db/migrations/migration --timestamp", "migration:run": "ts-node ./node_modules/typeorm/cli.js migration:run -d ./dist/config/data-source.js", "migration:force": "rm -rf ./db/migration && npm run migrate:generate && npm run migrate:run"}, "dependencies": {"@crednet/authmanager": "^0.2.8", "@crednet/utils": "^0.1.51", "@googlemaps/google-maps-services-js": "^3.4.1", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.1", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^10.4.7", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.1", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "aes-everywhere": "^1.0.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.4", "axios": "^1.7.7", "bullmq": "^5.38.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotnet": "^1.1.4", "mysql2": "^3.11.3", "nestjs-typeorm-paginate": "^4.0.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/bridgecard(|/.*)$": "<rootDir>/libs/bridgecard/src/$1", "^@app/miden(|/.*)$": "<rootDir>/libs/miden/src/$1", "^@app/verification(|/.*)$": "<rootDir>/libs/verification/src/$1", "^@app/geolocation(|/.*)$": "<rootDir>/libs/geolocation/src/$1"}}}