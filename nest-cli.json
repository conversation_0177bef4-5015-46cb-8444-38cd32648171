{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true}, "projects": {"bridgecard": {"type": "library", "root": "libs/bridgecard", "entryFile": "index", "sourceRoot": "libs/bridgecard/src", "compilerOptions": {"tsConfigPath": "libs/bridgecard/tsconfig.lib.json"}}, "miden": {"type": "library", "root": "libs/miden", "entryFile": "index", "sourceRoot": "libs/miden/src", "compilerOptions": {"tsConfigPath": "libs/miden/tsconfig.lib.json"}}, "verification": {"type": "library", "root": "libs/verification", "entryFile": "index", "sourceRoot": "libs/verification/src", "compilerOptions": {"tsConfigPath": "libs/verification/tsconfig.lib.json"}}, "geolocation": {"type": "library", "root": "libs/geolocation", "entryFile": "index", "sourceRoot": "libs/geolocation/src", "compilerOptions": {"tsConfigPath": "libs/geolocation/tsconfig.lib.json"}}}}