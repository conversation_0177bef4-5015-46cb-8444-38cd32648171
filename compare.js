const array1 = [
    "f32e4481-a7d6-44fb-8ec0-1917a1d672de",
    "93aa991b-adf8-44e3-9cff-c690acc23858",
    "a3d9fcf9-c8b7-4de1-8b7a-72f05ac5fb35",
    "e9570657-d8ab-4522-b606-4a96a516b91a",
    "a9ddc881-866d-4429-97c9-953940a69066",
    "c5c7590e-9a12-4c3b-b87d-6d8b1c0790a5",
    "0bd7cbdd-52a1-49c9-93b8-2604a5fe757b",
    "9003e0b6-be1a-4246-b45e-06b9c42e472f",
    "f6160fa3-a470-4257-a829-0a96c0ee0690",
    "6960afa3-95ed-48fc-bec5-f44485e58e9a",
    "1cfef935-b5ad-46fb-a0a1-ffb70049d31d",
    "eb952ecc-227d-49ab-ad7b-1ff8105ca77c",
    "76b19936-3cab-4d33-87c7-130cdf359a89",
    "5bfb4110-0106-4836-b6a4-af4485dc2e43",
    "a9f9f85b-c71d-4c2c-af19-a2089dd0ea4e",
    "aafe534b-00f6-44da-96ae-c4b680ea9655",
    "15d80cb1-1b7a-4570-b7c4-46fcf0de2a1e",
    "e6a0e1dc-b7eb-47e4-baec-f1ed50a8974b",
    "81b7d149-01a9-4081-b24e-718218e31da5",
    "1e27485e-47db-4013-9ac7-47a68773bea0",
    "11be8f00-654c-43e3-8116-47f4031225a2",
    "b335cc76-98bb-423e-9ca1-3ee99b78dbc8",
    "4b6754d3-4e48-4075-b979-a3eb7e7377b3",
    "9b1c78ea-89e2-465b-bfd3-4fff4a4999fd",
    "6ed407bf-d891-4c40-bc51-8376608dbc93",
    "70aa93a7-e134-47a3-9cad-8556621de7a0",
    "8374204f-c2ae-403f-a11a-775f1c4b13cc",
    "19d37337-dd6f-4a13-8ff4-74638dcf5f72",
    "40752a44-35f6-4497-88ee-dd914245c82d",
    "9af5fe61-cb1f-4b32-931d-8c333fa9410c",
    "c91560d5-d974-43e6-a50e-461ec6d249ff",
    "dfe11975-9ae0-4258-b68a-061fedc9382b",
    "24bb3ceb-94e2-4b10-b091-328c03651122",
    "24c664b4-beef-4b21-8dde-7a0673df029c",
    "0c896b06-a153-43c0-a75d-e533bdc4abbd"
];

const array2 = [
    "0c896b06-a153-43c0-a75d-e533bdc4abbd",
    "11be8f00-654c-43e3-8116-47f4031225a2",
    "11be8f00-654c-43e3-8116-47f4031225a2",
    "19d37337-dd6f-4a13-8ff4-74638dcf5f72",
    "1cfef935-b5ad-46fb-a0a1-ffb70049d31d",
    "1cfef935-b5ad-46fb-a0a1-ffb70049d31d",
    "24bb3ceb-94e2-4b10-b091-328c03651122",
    "24bb3ceb-94e2-4b10-b091-328c03651122",
    "9003e0b6-be1a-4246-b45e-06b9c42e472f",
    "9003e0b6-be1a-4246-b45e-06b9c42e472f",
    "93aa991b-adf8-44e3-9cff-c690acc23858",
    "93aa991b-adf8-44e3-9cff-c690acc23858",
    "9af5fe61-cb1f-4b32-931d-8c333fa9410c",
    "9af5fe61-cb1f-4b32-931d-8c333fa9410c",
    "a9ddc881-866d-4429-97c9-953940a69066",
    "a9ddc881-866d-4429-97c9-953940a69066",
    "a9ddc881-866d-4429-97c9-953940a69066",
    "c5c7590e-9a12-4c3b-b87d-6d8b1c0790a5",
    "c5c7590e-9a12-4c3b-b87d-6d8b1c0790a5",
    "c91560d5-d974-43e6-a50e-461ec6d249ff",
    "dfe11975-9ae0-4258-b68a-061fedc9382b"
];

// Find IDs that are in array1 but not in array2
const missingIds = array1.filter(id => !array2.includes(id));

// Output as a copyable array
console.log(JSON.stringify(missingIds, null, 2)); 