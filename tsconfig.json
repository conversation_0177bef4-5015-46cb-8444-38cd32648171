{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/bridgecard": ["libs/bridgecard/src"], "@app/bridgecard/*": ["libs/bridgecard/src/*"], "@app/miden": ["libs/miden/src"], "@app/miden/*": ["libs/miden/src/*"], "@app/verification": ["libs/verification/src"], "@app/verification/*": ["libs/verification/src/*"], "@app/geolocation": ["libs/geolocation/src"], "@app/geolocation/*": ["libs/geolocation/src/*"], "@app/wema": ["libs/wema/src"], "@app/wema/*": ["libs/wema/src/*"]}}}